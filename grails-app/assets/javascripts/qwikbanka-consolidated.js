/**
 * QwikBanka Core Banking System - Consolidated JavaScript
 * Modern ES6+ compatible scripts with banking-specific functionality
 * 
 * <AUTHOR> Development Team
 * @version 2.0
 * @since Grails 6.2.3
 */

// ========================================
// QWIKBANKA CORE NAMESPACE
// ========================================
window.QwikBanka = window.QwikBanka || {};

QwikBanka.Config = {
    IDLE_TIMEOUT: 1800, // 30 minutes default
    APP_TITLE: "QwikBanka Core Banking System",
    API_BASE_URL: window.location.origin,
    CSRF_TOKEN: document.querySelector('meta[name="_csrf"]')?.getAttribute('content'),
    CSRF_HEADER: document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content'),
    
    // Banking specific configurations
    CURRENCY_PRECISION: 2,
    MAX_TRANSACTION_AMOUNT: 1000000,
    SESSION_WARNING_TIME: 300, // 5 minutes before timeout
    
    // UI Configuration
    DATATABLE_PAGE_SIZE: 25,
    SEARCH_DEBOUNCE_TIME: 300,
    ANIMATION_DURATION: 300
};

// ========================================
// UTILITY FUNCTIONS
// ========================================
QwikBanka.Utils = {
    /**
     * Format currency with proper banking precision
     */
    formatCurrency: function(amount, currency = 'PHP') {
        if (typeof amount !== 'number') {
            amount = parseFloat(amount) || 0;
        }
        
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: QwikBanka.Config.CURRENCY_PRECISION,
            maximumFractionDigits: QwikBanka.Config.CURRENCY_PRECISION
        }).format(amount);
    },

    /**
     * Format date for banking display
     */
    formatDate: function(date, format = 'short') {
        if (!date) return '';
        
        const dateObj = date instanceof Date ? date : new Date(date);
        
        const options = {
            short: { year: 'numeric', month: '2-digit', day: '2-digit' },
            long: { year: 'numeric', month: 'long', day: 'numeric' },
            datetime: { 
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit', second: '2-digit'
            }
        };
        
        return dateObj.toLocaleDateString('en-PH', options[format] || options.short);
    },

    /**
     * Debounce function for search and input handling
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Validate account number format
     */
    validateAccountNumber: function(accountNo) {
        // Basic validation - can be enhanced based on bank's format
        const pattern = /^\d{10,16}$/;
        return pattern.test(accountNo);
    },

    /**
     * Generate unique transaction reference
     */
    generateTransactionRef: function() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `TXN${timestamp}${random}`;
    },

    /**
     * Safe AJAX request with CSRF protection
     */
    ajaxRequest: function(options) {
        const defaults = {
            type: 'POST',
            dataType: 'json',
            beforeSend: function(xhr) {
                if (QwikBanka.Config.CSRF_TOKEN) {
                    xhr.setRequestHeader(QwikBanka.Config.CSRF_HEADER, QwikBanka.Config.CSRF_TOKEN);
                }
            },
            error: function(xhr, status, error) {
                QwikBanka.UI.showError('Request failed: ' + error);
            }
        };
        
        return $.ajax($.extend(defaults, options));
    }
};

// ========================================
// UI COMPONENTS
// ========================================
QwikBanka.UI = {
    /**
     * Show success message using modern alerts
     */
    showSuccess: function(message, title = 'Success') {
        alertify.success(message);
    },

    /**
     * Show error message using modern alerts
     */
    showError: function(message, title = 'Error') {
        alertify.error(message);
    },

    /**
     * Show warning message using modern alerts
     */
    showWarning: function(message, title = 'Warning') {
        alertify.warning(message);
    },

    /**
     * Show confirmation dialog
     */
    confirm: function(message, onConfirm, onCancel, title = 'Confirm') {
        alertify.confirm(title, message, onConfirm, onCancel);
    },

    /**
     * Initialize modern DataTables
     */
    initDataTable: function(selector, options = {}) {
        const defaults = {
            pageLength: QwikBanka.Config.DATATABLE_PAGE_SIZE,
            responsive: true,
            processing: true,
            language: {
                processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>'
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            buttons: [
                {
                    extend: 'print',
                    className: 'btn btn-outline-secondary btn-sm'
                },
                {
                    extend: 'excel',
                    className: 'btn btn-outline-success btn-sm'
                }
            ]
        };
        
        return $(selector).DataTable($.extend(defaults, options));
    },

    /**
     * Initialize modern form validation
     */
    initFormValidation: function(formSelector) {
        $(formSelector).on('submit', function(e) {
            const form = this;
            let isValid = true;
            
            // Clear previous validation states
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
            
            // Validate required fields
            $(form).find('[required]').each(function() {
                if (!this.value.trim()) {
                    $(this).addClass('is-invalid');
                    $(this).after('<div class="invalid-feedback">This field is required.</div>');
                    isValid = false;
                }
            });
            
            // Validate account numbers
            $(form).find('[data-validate="account"]').each(function() {
                if (this.value && !QwikBanka.Utils.validateAccountNumber(this.value)) {
                    $(this).addClass('is-invalid');
                    $(this).after('<div class="invalid-feedback">Invalid account number format.</div>');
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                QwikBanka.UI.showError('Please correct the errors in the form.');
            }
        });
    },

    /**
     * Initialize modern select2 dropdowns
     */
    initSelect2: function(selector, options = {}) {
        const defaults = {
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: 'Select an option...',
            allowClear: true
        };
        
        return $(selector).select2($.extend(defaults, options));
    },

    /**
     * Initialize modern date pickers
     */
    initDatePicker: function(selector, options = {}) {
        const defaults = {
            format: 'mm/dd/yyyy',
            autoclose: true,
            todayHighlight: true,
            orientation: 'bottom auto'
        };
        
        return $(selector).datepicker($.extend(defaults, options));
    }
};

// ========================================
// SESSION MANAGEMENT
// ========================================
QwikBanka.Session = {
    idleTimer: null,
    warningTimer: null,
    
    /**
     * Initialize session timeout handling
     */
    init: function() {
        this.resetIdleTimer();
        
        // Reset timer on user activity
        $(document).on('mousedown keydown scroll touchstart', () => {
            this.resetIdleTimer();
        });
    },

    /**
     * Reset the idle timer
     */
    resetIdleTimer: function() {
        clearTimeout(this.idleTimer);
        clearTimeout(this.warningTimer);
        
        // Set warning timer
        this.warningTimer = setTimeout(() => {
            this.showSessionWarning();
        }, (QwikBanka.Config.IDLE_TIMEOUT - QwikBanka.Config.SESSION_WARNING_TIME) * 1000);
        
        // Set logout timer
        this.idleTimer = setTimeout(() => {
            this.handleSessionTimeout();
        }, QwikBanka.Config.IDLE_TIMEOUT * 1000);
    },

    /**
     * Show session timeout warning
     */
    showSessionWarning: function() {
        QwikBanka.UI.confirm(
            'Your session will expire in 5 minutes. Do you want to continue?',
            () => {
                this.resetIdleTimer();
                QwikBanka.UI.showSuccess('Session extended successfully.');
            },
            () => {
                this.handleSessionTimeout();
            },
            'Session Timeout Warning'
        );
    },

    /**
     * Handle session timeout
     */
    handleSessionTimeout: function() {
        QwikBanka.UI.showWarning('Your session has expired. You will be redirected to the login page.');
        setTimeout(() => {
            window.location.href = '/authentication/logout';
        }, 3000);
    }
};

// ========================================
// SEARCH FUNCTIONALITY
// ========================================
QwikBanka.Search = {
    /**
     * Initialize global search functionality
     */
    init: function() {
        const searchInput = $('#menu-search');
        const searchResults = $('.search_results');
        
        if (searchInput.length) {
            const debouncedSearch = QwikBanka.Utils.debounce(this.performSearch, QwikBanka.Config.SEARCH_DEBOUNCE_TIME);
            
            searchInput.on('input', function() {
                const query = $(this).val().trim();
                if (query.length >= 2) {
                    debouncedSearch(query);
                    searchResults.show();
                } else {
                    searchResults.hide();
                }
            });
            
            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.menu_search_wrapper').length) {
                    searchResults.hide();
                }
            });
        }
    },

    /**
     * Perform search and update results
     */
    performSearch: function(query) {
        const resultsList = $('#ul-results');
        const menuItems = resultsList.find('li:not(#no-results)');
        let hasResults = false;
        
        menuItems.each(function() {
            const text = $(this).text().toLowerCase();
            if (text.includes(query.toLowerCase())) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });
        
        $('#no-results').toggle(!hasResults);
    }
};

// ========================================
// BANKING SPECIFIC FUNCTIONS
// ========================================
QwikBanka.Banking = {
    /**
     * Calculate interest for given principal, rate, and time
     */
    calculateSimpleInterest: function(principal, rate, time) {
        return (principal * rate * time) / 100;
    },

    /**
     * Calculate compound interest
     */
    calculateCompoundInterest: function(principal, rate, time, frequency = 1) {
        return principal * Math.pow((1 + rate / (100 * frequency)), frequency * time) - principal;
    },

    /**
     * Validate transaction amount
     */
    validateTransactionAmount: function(amount) {
        const numAmount = parseFloat(amount);
        return numAmount > 0 && numAmount <= QwikBanka.Config.MAX_TRANSACTION_AMOUNT;
    },

    /**
     * Format account number for display
     */
    formatAccountNumber: function(accountNo) {
        if (!accountNo) return '';
        return accountNo.toString().replace(/(\d{4})(?=\d)/g, '$1-');
    }
};

// ========================================
// INITIALIZATION
// ========================================
$(document).ready(function() {
    // Initialize core components
    QwikBanka.Session.init();
    QwikBanka.Search.init();
    
    // Initialize UI components
    QwikBanka.UI.initSelect2('.select2');
    QwikBanka.UI.initDatePicker('.datepicker');
    QwikBanka.UI.initFormValidation('form[data-validate="true"]');
    
    // Initialize DataTables
    if ($.fn.DataTable) {
        $('.data-table').each(function() {
            QwikBanka.UI.initDataTable(this);
        });
    }
    
    // Initialize tooltips and popovers
    $('[data-bs-toggle="tooltip"]').tooltip();
    $('[data-bs-toggle="popover"]').popover();
    
    // Currency formatting for amount fields
    $('.currency-input').on('blur', function() {
        const value = parseFloat($(this).val()) || 0;
        $(this).val(value.toFixed(QwikBanka.Config.CURRENCY_PRECISION));
    });
    
    // Account number formatting
    $('.account-input').on('blur', function() {
        const value = $(this).val();
        if (value) {
            $(this).val(QwikBanka.Banking.formatAccountNumber(value));
        }
    });
    
    console.log('QwikBanka Core Banking System initialized successfully');
});

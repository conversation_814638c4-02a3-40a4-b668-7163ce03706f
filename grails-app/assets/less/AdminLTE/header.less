/*
    Components: navbar, logo and content header
-------------------------------------------------
*/

body > .header {
    position: relative;
    max-height: 100px;   
    z-index: 1030;
    .navbar {
        height: 50px;
        margin-bottom: 0;        
        margin-left: @left-side-width;
    }

    //Navbar toggle button
    .navbar .sidebar-toggle {
        float: left;
        padding: 9px 5px;
        margin-top: 8px;
        margin-right: 0;
        margin-bottom: 8px;
        margin-left: 5px;
        background-color: transparent;
        background-image: none;
        border: 1px solid transparent;
        .border-radius(0)!important;
    }

    .navbar .sidebar-toggle:hover .icon-bar{
        background: #f6f6f6;
    }
    .navbar .sidebar-toggle .icon-bar {
        display: block;
        width: 22px;
        height: 2px;

        .border-radius(4px);

    }

    .navbar .sidebar-toggle .icon-bar + .icon-bar {
        margin-top: 4px;
    }

    //User image in navbar
    .navbar .nav > li.user > a {
        font-weight: bold;
    }
    .navbar .nav > li.user > a > .fa,
    .navbar .nav > li.user > a > .glyphicon,
    .navbar .nav > li.user > a > .ion {
        margin-right: 5px;
    }

    //Labels in navbar
    .navbar .nav > li > a > .label {
        .border-radius(50%);
        position: absolute;
        top:7px;
        right: 2px;
        font-size: 10px;
        font-weight: normal;
        width: 15px;
        height: 15px;
        line-height: 1.0em;
        text-align: center;
        padding: 2px;
    }
    .navbar .nav > li > a:hover > .label {
        top: 3px;
    }

    //Logo bar - leftside
    .logo {
        float: left;
        height: 50px;
        font-size: 20px;
        line-height: 50px;
        text-align: center;
        padding: 0 10px;
        width: @left-side-width;
        font-family: 'Kaushan Script', cursive;
        font-weight: 500;
        height: 50px;
        display: block;
        //For logo imgs or icons
        .icon {
            margin-right: 10px;
        }        
    }

}

// Content Header 
.right-side > .content-header {
    position: relative;
    padding: 15px 15px 10px 20px;    
    // Header Text 
    > h1 {
        margin: 0;
        font-size: 24px;
        > small {
            font-size: 15px;
            display: inline-block;
            padding-left: 4px;
            font-weight: 300;
        }
    }

    > .breadcrumb {
        float: right;
        background: transparent;
        margin-top: 0px;
        margin-bottom: 0;
        font-size: 12px;
        padding: 7px 5px;
        position: absolute;
        top: 15px;
        right: 10px;
        .border-radius(2px);
        > li > a {
            color: #444;
            text-decoration: none;
            > .fa, > .glyphicon, > .ion {
                margin-right: 5px;
            }            
        }
        > li + li:before {
            content: '>\00a0';
        }
    }

    @media screen and (max-width: @screen-sm) {
        > .breadcrumb {
            position: relative;
            margin-top: 5px;
            top: 0;
            right: 0;
            float: none;
            background: #efefef;
        }
    }
}

//Control navbar scaffolding on x-small screens
@media (max-width: @screen-sm) {
    .navbar .navbar-nav > li {
        float: left;
    }
    //Dont't let links get full width
    .navbar-nav {
        margin: 0;
        float: left;
    }

    .navbar-nav > li > a {
        padding-top: 15px;
        padding-bottom: 15px;
        line-height: 20px;
    }
    .navbar .navbar-right {
        float: right;
    }
}

// Collapse header 
@media screen and (max-width: @screen-header-collapse) {
    body > .header {
        position: relative;
        .logo, .navbar {
            width: 100%;
            float: none;
            position: relative!important;
        }
        .navbar {
            margin: 0;
        }

    }

    body.fixed {
        > .header {
            position: fixed;
        }
        > .wrapper, .sidebar-offcanvas {
            margin-top: 100px!important;
        }
    }

}
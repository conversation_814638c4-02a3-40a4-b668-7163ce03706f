/**
 * QwikBanka Core Banking System - Consolidated CSS
 * Modern Bootstrap 5 compatible styles with banking-specific enhancements
 * 
 * <AUTHOR> Development Team
 * @version 2.0
 * @since Grails 6.2.3
 */

/* ========================================
   BANKING SYSTEM VARIABLES
   ======================================== */
:root {
    /* Primary Banking Colors */
    --qb-primary: #1e3a8a;
    --qb-secondary: #64748b;
    --qb-success: #059669;
    --qb-danger: #dc2626;
    --qb-warning: #d97706;
    --qb-info: #0284c7;
    
    /* Banking UI Colors */
    --qb-sidebar-bg: #1e293b;
    --qb-header-bg: #0f172a;
    --qb-content-bg: #f8fafc;
    --qb-border-color: #e2e8f0;
    
    /* Typography */
    --qb-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --qb-font-size-base: 0.875rem;
    --qb-line-height-base: 1.5;
    
    /* Spacing */
    --qb-spacing-xs: 0.25rem;
    --qb-spacing-sm: 0.5rem;
    --qb-spacing-md: 1rem;
    --qb-spacing-lg: 1.5rem;
    --qb-spacing-xl: 3rem;
    
    /* Shadows */
    --qb-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --qb-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --qb-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --qb-border-radius: 0.375rem;
    --qb-border-radius-lg: 0.5rem;
}

/* ========================================
   GLOBAL STYLES
   ======================================== */
body {
    font-family: var(--qb-font-family);
    font-size: var(--qb-font-size-base);
    line-height: var(--qb-line-height-base);
    background-color: var(--qb-content-bg);
    color: #1e293b;
}

/* ========================================
   HEADER STYLES
   ======================================== */
.header {
    background: linear-gradient(135deg, var(--qb-header-bg) 0%, var(--qb-primary) 100%);
    box-shadow: var(--qb-shadow-md);
    position: sticky;
    top: 0;
    z-index: 1030;
}

#logo-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--qb-spacing-md);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.logo img {
    max-height: 50px;
    width: auto;
    border-radius: var(--qb-border-radius);
}

#user-bar {
    background-color: var(--qb-header-bg);
    padding: var(--qb-spacing-sm) var(--qb-spacing-md);
}

#user-bar ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    justify-content: flex-end;
    align-items: center;
    gap: var(--qb-spacing-md);
}

#user-bar a {
    color: #e2e8f0;
    text-decoration: none;
    padding: var(--qb-spacing-sm) var(--qb-spacing-md);
    border-radius: var(--qb-border-radius);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--qb-spacing-xs);
}

#user-bar a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* ========================================
   SIDEBAR NAVIGATION
   ======================================== */
#sidebar_menu {
    background-color: var(--qb-sidebar-bg);
    width: 280px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1020;
    overflow-y: auto;
    box-shadow: var(--qb-shadow-lg);
    transition: transform 0.3s ease;
}

.sidebar_header {
    padding: var(--qb-spacing-lg);
    background: linear-gradient(135deg, var(--qb-primary) 0%, #3b82f6 100%);
    color: white;
    text-align: center;
}

.sidebar_header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.sidebar_nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav_item > a {
    display: flex;
    align-items: center;
    padding: var(--qb-spacing-md);
    color: #cbd5e1;
    text-decoration: none;
    border-bottom: 1px solid #334155;
    transition: all 0.3s ease;
    gap: var(--qb-spacing-md);
}

.nav_item > a:hover {
    background-color: #334155;
    color: #ffffff;
    padding-left: calc(var(--qb-spacing-md) + var(--qb-spacing-xs));
}

.nav-icon {
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    flex-shrink: 0;
}

/* Icon styles */
.icon-custinfo { background-image: url('../images/icon-custinfo.png'); }
.icon-deposit { background-image: url('../images/icon-deposit.png'); }
.icon-loan { background-image: url('../images/icon-loan.png'); }
.icon-tellertxn { background-image: url('../images/icon-tellertxn.png'); }
.icon-ledger { background-image: url('../images/icon-ledger.png'); }
.icon-sysad { background-image: url('../images/icon-sysad.png'); }
.icon-config { background-image: url('../images/icon-config.png'); }
.icon-audit { background-image: url('../images/icon-audit.png'); }

/* Submenu styles */
.nav_item ul {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #0f172a;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav_item:hover ul {
    max-height: 500px;
}

.nav_item ul li a {
    display: block;
    padding: var(--qb-spacing-sm) var(--qb-spacing-md) var(--qb-spacing-sm) calc(var(--qb-spacing-md) * 3);
    color: #94a3b8;
    text-decoration: none;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.nav_item ul li a:hover {
    background-color: #1e293b;
    color: #ffffff;
    padding-left: calc(var(--qb-spacing-md) * 3 + var(--qb-spacing-xs));
}

/* ========================================
   MAIN CONTENT AREA
   ======================================== */
.bodywrap {
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.bodywrap.withsidebar {
    margin-left: 280px;
}

#body-gradient {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--qb-content-bg) 0%, #f1f5f9 100%);
}

/* ========================================
   BREADCRUMBS
   ======================================== */
#breadcrumbs {
    background: linear-gradient(90deg, var(--qb-primary) 0%, #3b82f6 100%);
    padding: var(--qb-spacing-sm) var(--qb-spacing-md);
    color: white;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#breadcrumbs a {
    color: #e2e8f0;
    text-decoration: none;
    transition: color 0.3s ease;
}

#breadcrumbs a:hover {
    color: #ffffff;
}

/* ========================================
   SEARCH FUNCTIONALITY
   ======================================== */
.menu_search_wrapper {
    position: relative;
    max-width: 400px;
}

.search_form {
    position: relative;
    display: flex;
    align-items: center;
}

#input-search-icon {
    position: absolute;
    left: var(--qb-spacing-md);
    color: #64748b;
    z-index: 10;
}

#menu-search {
    padding-left: calc(var(--qb-spacing-md) * 2.5);
    padding-right: calc(var(--qb-spacing-md) * 2.5);
    border: 1px solid var(--qb-border-color);
    border-radius: var(--qb-border-radius);
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

.close_search {
    position: absolute;
    right: var(--qb-spacing-md);
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    z-index: 10;
}

.search_results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--qb-border-color);
    border-top: none;
    border-radius: 0 0 var(--qb-border-radius) var(--qb-border-radius);
    box-shadow: var(--qb-shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search_results ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search_results li a {
    display: block;
    padding: var(--qb-spacing-sm) var(--qb-spacing-md);
    color: #374151;
    text-decoration: none;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.3s ease;
}

.search_results li a:hover {
    background-color: var(--qb-content-bg);
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */
@media (max-width: 768px) {
    #sidebar_menu {
        transform: translateX(-100%);
    }
    
    .bodywrap.withsidebar {
        margin-left: 0;
    }
    
    #logo-bar {
        flex-direction: column;
        gap: var(--qb-spacing-md);
    }
    
    .location {
        text-align: center;
    }
    
    #user-bar ul {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* ========================================
   UTILITY CLASSES
   ======================================== */
.qb-card {
    background: white;
    border-radius: var(--qb-border-radius-lg);
    box-shadow: var(--qb-shadow-sm);
    border: 1px solid var(--qb-border-color);
}

.qb-btn-primary {
    background: linear-gradient(135deg, var(--qb-primary) 0%, #3b82f6 100%);
    border: none;
    color: white;
    padding: var(--qb-spacing-sm) var(--qb-spacing-lg);
    border-radius: var(--qb-border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.qb-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--qb-shadow-md);
}

.qb-text-primary { color: var(--qb-primary); }
.qb-text-success { color: var(--qb-success); }
.qb-text-danger { color: var(--qb-danger); }
.qb-text-warning { color: var(--qb-warning); }

.qb-bg-primary { background-color: var(--qb-primary); }
.qb-bg-light { background-color: var(--qb-content-bg); }

/* ========================================
   PRINT STYLES
   ======================================== */
@media print {
    .hidden-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .qb-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

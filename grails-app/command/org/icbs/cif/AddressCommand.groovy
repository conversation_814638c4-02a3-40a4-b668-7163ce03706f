package org.icbs.cif

import org.icbs.lov.AddressStatus
import org.icbs.lov.AddressType
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.Language
import org.icbs.lov.Lov
import org.icbs.lov.Town
import grails.validation.Validateable

@Validateable
class AddressCommand {

    AddressType type
    boolean isPrimary
    boolean isMailing
    boolean isOwned
    boolean isMortaged
    boolean isRented
    String address1
    String address2
    String address3
    String postalCode
    String residenceSince
    Lov countryId
    Town town
    String phone1
    String phone2
    String phone3
    String phone4
    Date addressSince
    String shortAddress
    String longLatLocation
    String addressContact
    String remarks
    ConfigItemStatus status
    Lov languageId

    static constraints = {
        type nullable: true
        status nullable: true
        languageId nullable: true
        countryId nullable: true
        address1 nullable: false, maxSize: 200
        address2 nullable: false, maxSize: 200
        address3 nullable: false, maxSize: 100
        town nullable: false, maxSize: 100
        postalCode nullable: true, maxSize: 10
        shortAddress nullable: true, maxSize: 100
        addressContact nullable: true, maxSize: 30
        phone1 nullable: true, maxSize: 30
        phone2 nullable: true, maxSize: 30
        phone3 nullable: true, maxSize: 30
        phone4 nullable: true, maxSize: 30
        residenceSince nullable: true, maxSize: 10
        addressSince nullable: true
        longLatLocation nullable: true, maxSize: 30
        remarks nullable: true, maxSize: 255
        isRented nullable: true
    }
} 
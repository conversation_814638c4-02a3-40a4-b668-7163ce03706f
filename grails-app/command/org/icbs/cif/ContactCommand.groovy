package org.icbs.cif

import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.Lov
import grails.validation.Validateable

@Validateable
class ContactCommand {

    boolean isPrimaryPhone
    boolean isPrimaryEmail
    Lov type
    String contactValue
    ConfigItemStatus status

    static constraints = {
        status nullable: true
        type nullable: false
        contactValue nullable: false, maxSize: 50
    }
} 
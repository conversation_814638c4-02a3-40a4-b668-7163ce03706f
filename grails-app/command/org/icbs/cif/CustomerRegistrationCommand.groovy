package org.icbs.cif

import org.icbs.lov.CustomerDosriCode
import org.icbs.lov.CustomerStatus
import org.icbs.lov.CustomerType
import org.icbs.lov.FirmSize
import org.icbs.lov.Gender
import org.icbs.lov.Lov
import org.icbs.lov.Religion
import org.icbs.lov.ResidentType
import org.icbs.lov.RiskType
import org.icbs.admin.CustomerGroup
import org.icbs.admin.UserMaster
import org.icbs.deposit.Signatory
import org.icbs.admin.Branch
import org.icbs.deposit.Deposit
import org.icbs.gl.CfgAcctGlTemplate
import org.icbs.loans.Loan
import org.icbs.lov.Town
import grails.validation.Validateable
import grails.gorm.MultiTenant
import java.util.Calendar

@Validateable
class CustomerRegistrationCommand implements MultiTenant {

    // Fields from Customer Domain relevant for registration
    CustomerType type
    Branch branch
    String customerId
    String name1
    String name2
    String name3
    String name4
    Lov name5
    String displayName
    String shortAddress
    String pepDescription
    String amla
    Date birthDate
    Lov title
    Gender gender
    Lov civilStatus
    String birthPlace
    boolean isTaxable
    double creditLimit
    ResidentType customerCode1
    RiskType customerCode2
    FirmSize customerCode3
    Lov nationality
    String sourceOfIncome
    CustomerDosriCode dosriCode
    String sssNo
    String gisNo
    String tinNo
    String passportNo
    String remarks
    CustomerGroup group
    CustomerStatus status
    Integer noOfDependent
    String motherMaidenName
    String fatherName
    Religion religion
    String spouseLastName
    String spouseFirstName
    String spouseMiddleName
    Date spouseBirthDate
    String spouseContactNo
    Membership membership
    Town custBirthPlace

    // Nested Command Objects or collection properties for associated data
    List<ContactCommand> contacts
    List<AddressCommand> addresses
    // Add other associated entities as Command Objects if they are part of the initial registration
    // List<AttachmentCommand> attachments // If attachments are directly uploaded with the form

    static constraints = {
        type nullable: false
        branch nullable: true
        gender nullable: false
        dosriCode nullable: false
        status nullable: true
        title nullable: false
        civilStatus nullable: false
        customerCode1 nullable: true
        customerCode2 nullable: true
        customerCode3 nullable: true
        amla nullable: true
        pepDescription nullable: true
        nationality nullable: true
        customerId nullable: true
        name1 size: 2..50, nullable: false
        name3 size: 2..50, validator: {
            val, obj, errors ->
            if (obj?.type?.id == 1) {
                if (val == null) {
                    errors.rejectValue('name3', "customer.name3.blank.label")
                }
            } else {
                return true
            }
        }, nullable: true
        name2 nullable: true
        name4 nullable: true, size: 2..50
        name5 nullable: true
        displayName nullable: true, blank: false, maxSize: 255
        shortAddress nullable: true, size: 2..50
        birthDate nullable: false, blank: false, validator: {
            val, obj, errors ->
            if (obj?.type?.id == 1) {
                if (obj.hasProperty('status')) {
                    if (obj.status?.id == 5) {
                        return true
                    } else {
                        // BirthDate cannot be greater than system date
                        if (val >= Branch.get(1).runDate) {
                            errors.rejectValue('birthDate', "customer.birthDate.future.label")
                        }
                        // Custom validation: Customer must be at least 18 years old
                        Calendar eighteenYearsAgo = Calendar.getInstance()
                        eighteenYearsAgo.add(Calendar.YEAR, -18)
                        if (val > eighteenYearsAgo.getTime()) {
                            errors.rejectValue('birthDate', "customer.birthDate.underAge.label")
                        }
                    }
                }
            } else {
                return true
            }
        }
        birthPlace maxSize: 50, validator: {
            val, obj, errors ->
            if (obj?.type?.id == 1) {
                if (obj.hasProperty('status')) {
                    if (obj.status?.id == 5) {
                        return true
                    } else {
                        if (val == null) {
                            errors.rejectValue('birthPlace', "customer.birthPlace.blank.label")
                        }
                    }
                }
            } else {
                return true
            }
        }, nullable: false
        isTaxable nullable: true
        sourceOfIncome nullable: false
        creditLimit nullable: true, min: 0D, scale: 2
        sssNo nullable: true, maxSize: 50
        gisNo nullable: true, maxSize: 50
        tinNo nullable: true, maxSize: 50
        passportNo nullable: true, maxSize: 50
        remarks nullable: true, maxSize: 255
        group nullable: true
        noOfDependent nullable: true
        motherMaidenName nullable: true
        fatherName nullable: true
        religion nullable: true
        spouseLastName nullable: true
        spouseFirstName nullable: true
        spouseMiddleName nullable: true
        spouseBirthDate nullable: true
        spouseContactNo nullable: true
        membership nullable: true
        custBirthPlace nullable: true

        // Collection validations using the implicit validateable cascade
        contacts nullable: true, validator: {
            contacts ->
            if (contacts) {
                contacts.eachWithIndex { contact, i ->
                    if (!contact.validate()) {
                        contact.errors.allErrors.each { error ->
                            delegate.errors.rejectValue("contacts[${i}].${error.field}", error.code, error.arguments, error.defaultMessage)
                        }
                    }
                }
            }
        }
        addresses nullable: true, validator: {
            addresses ->
            if (addresses) {
                addresses.eachWithIndex { address, i ->
                    if (!address.validate()) {
                        address.errors.allErrors.each { error ->
                            delegate.errors.rejectValue("addresses[${i}].${error.field}", error.code, error.arguments, error.defaultMessage)
                        }
                    }
                }
            }
        }
    }
}

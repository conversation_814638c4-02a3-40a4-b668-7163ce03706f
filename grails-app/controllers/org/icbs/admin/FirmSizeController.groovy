package org.icbs.admin

import org.icbs.lov.FirmSize
import org.springframework.dao.DataIntegrityViolationException
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON

/**
 * FirmSizeController - Handles firm size maintenance
 * 
 * This controller manages firm size operations including:
 * - Firm size listing and search
 * - Firm size creation and editing
 * - Firm size updates and deletion
 * - Firm size validation
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class FirmSizeController {
    
    // Service Dependencies
    def auditLogService
    
    static allowedMethods = [
        save: "POST", 
        update: "POST", 
        delete: "POST"
    ]

    /**
     * Main index page
     */
    def index() {
        redirect(action: "sizeOfFirmIndex")
    }
    
    /**
     * List all firm sizes
     */
    def sizeOfFirmIndex() {
        def firmSize = FirmSize.list(sort: "code")
        
        def description = "Firm size listing accessed"
        auditLogService.insert('140', 'ADM00100', description, 'FirmSize', null, null, null, null)
        
        respond firmSize
    }
    
    /**
     * Edit firm size instance
     */
    def firmSizeInstanceEdit() {
        def firmSize = FirmSize.get(params.id)
        if (firmSize) {
            respond firmSize
        } else {
            flash.message = message(code: 'default.not.found.message', args: ['FirmSize', params.id])
            redirect(action: "sizeOfFirmIndex")
        }           
    }
    
    /**
     * Update firm size instance
     */
    @Transactional
    def firmSizeInstanceUpdate(Long id, Long version) {
        def firmSizeInstance = FirmSize.get(id)
        
        if (!firmSizeInstance) {
            flash.message = message(code: 'default.not.found.message', args: ['FirmSize', id])
            redirect(action: "sizeOfFirmIndex")
            return
        }
        
        if (version != null && firmSizeInstance.version > version) {
            firmSizeInstance.errors.rejectValue("version", "default.optimistic.locking.failure",
                    [message(code: 'firmSize.label', default: 'FirmSize')] as Object[],
                    "Another user has updated this FirmSize while you were editing")
            render(view: "firmSizeInstanceEdit", model: [firmSizeInstance: firmSizeInstance])
            return
        }
        
        firmSizeInstance.properties = params
        
        if (!firmSizeInstance.save(flush: true)) {
            render(view: "firmSizeInstanceEdit", model: [firmSizeInstance: firmSizeInstance])
            return
        }
        
        def description = "Firm size updated: ${firmSizeInstance.code} - ${firmSizeInstance.description}"
        auditLogService.insert('140', 'ADM00200', description, 'FirmSize', null, null, null, firmSizeInstance.id)
        
        flash.success = message(code: 'default.updated.message', args: [message(code: 'firmSize.label', default: 'FirmSize'), firmSizeInstance.id])
        redirect(action: "sizeOfFirmIndex", id: firmSizeInstance.id)
    }
    
    /**
     * Create new firm size form
     */
    def firmSizeInstanceCreate() {
        [firmSizeInstance: new FirmSize(params)]  
    }
    
    /**
     * Save new firm size instance
     */
    @Transactional
    def firmSizeInstanceSave() {
        def firmSizeInstance = new FirmSize(params)
        
        if (!firmSizeInstance.save(flush: true)) {
            render(view: "firmSizeInstanceCreate", model: [firmSizeInstance: firmSizeInstance])
            return
        }
        
        def description = "Firm size created: ${firmSizeInstance.code} - ${firmSizeInstance.description}"
        auditLogService.insert('140', 'ADM00300', description, 'FirmSize', null, null, null, firmSizeInstance.id)
        
        flash.success = message(code: 'default.created.message', args: [message(code: 'firmSize.label', default: 'FirmSize'), firmSizeInstance.id])
        redirect(action: "sizeOfFirmIndex", id: firmSizeInstance.id)
    }
    
    /**
     * Delete firm size instance
     */
    @Transactional
    def firmSizeInstanceDelete(Long id) {
        def firmSizeInstance = FirmSize.get(id)
        if (!firmSizeInstance) {
            flash.message = message(code: 'default.not.found.message', args: [message(code: 'firmSize.label', default: 'FirmSize'), id])
            redirect(action: "sizeOfFirmIndex")
            return
        }
        
        try {
            def description = "Firm size deleted: ${firmSizeInstance.code} - ${firmSizeInstance.description}"
            auditLogService.insert('140', 'ADM00400', description, 'FirmSize', null, null, null, firmSizeInstance.id)
            
            firmSizeInstance.delete(flush: true)
            flash.success = message(code: 'default.deleted.message', args: [message(code: 'firmSize.label', default: 'FirmSize'), id])
            redirect(action: "sizeOfFirmIndex")
        }
        catch (DataIntegrityViolationException e) {
            flash.error = message(code: 'default.not.deleted.message', args: [message(code: 'firmSize.label', default: 'FirmSize'), id])
            redirect(action: "sizeOfFirmIndex", id: id)
        }
    }
    
    /**
     * Search firm sizes
     */
    def search() {
        def query = params.query?.trim()
        def firmSizes = []
        
        if (query) {
            firmSizes = FirmSize.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                order("code", "asc")
            }
        } else {
            firmSizes = FirmSize.list(sort: "code")
        }
        
        render(view: "sizeOfFirmIndex", model: [firmSizeList: firmSizes, query: query])
    }
    
    /**
     * Validate firm size code uniqueness
     */
    def validateCode() {
        def code = params.code?.trim()
        def id = params.id?.toLong()
        
        if (!code) {
            render([valid: false, message: "Code is required"] as JSON)
            return
        }
        
        def existing = FirmSize.createCriteria().get {
            eq("code", code)
            if (id) {
                ne("id", id)
            }
        }
        
        if (existing) {
            render([valid: false, message: "Code already exists"] as JSON)
        } else {
            render([valid: true, message: "Code is available"] as JSON)
        }
        return
    }
    
    /**
     * Get firm size details via AJAX
     */
    def getFirmSizeDetails() {
        def firmSize = FirmSize.get(params.id)
        
        if (firmSize) {
            render([
                id: firmSize.id,
                code: firmSize.code,
                description: firmSize.description,
                status: firmSize.status?.description,
                dateCreated: firmSize.dateCreated?.format("MM/dd/yyyy")
            ] as JSON)
        } else {
            render([error: "Firm size not found"] as JSON)
        }
        return
    }
}

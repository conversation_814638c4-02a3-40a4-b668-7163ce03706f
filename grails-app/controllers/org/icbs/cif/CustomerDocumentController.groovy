package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import org.icbs.admin.DocumentType
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import org.icbs.security.PermissionService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Document Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer document management operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerDocumentController {
    
    static namespace = 'api/v1'
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def fileStorageService
    def auditLogService
    def permissionService
    
    static allowedMethods = [
        create: "POST",
        showContent: "GET",
        delete: "DELETE",
        index: "GET",
        getDocumentChecklist: "GET",
        updateStatus: "POST"
    ]
    
    // =====================================================
    // CUSTOMER DOCUMENT OPERATIONS
    // =====================================================
    
    /**
     * Upload customer document
     */
    def create() {
        log.info("Uploading customer document")
        
        // Enforce permission: CUSTOMER_DOCUMENT_UPLOAD
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_DOCUMENT_UPLOAD")) {
            log.warn("User {} attempted to upload document without CUSTOMER_DOCUMENT_UPLOAD permission.", currentUser?.username)
            render([success: false, error: "You do not have permission to upload documents."] as JSON)
            auditLogService.insert('030', 'CDC01201', "Unauthorized attempt to upload document by user ${currentUser?.username}", 'CustomerDocumentController', null, null, 'cif/customerDocument/create', currentUser?.id)
            return
        }
        
        try {
            if (!params.customerId) {
                render([success: false, error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([success: false, error: 'Customer not found'] as JSON)
                return
            }
            
            // Process file upload
            Map uploadResult = processDocumentUpload(request, params, customer)
            
            if (uploadResult.success) {
                // Audit logging
                auditDocumentUpload(customer, uploadResult.document, 'SUCCESS')
                
                render([
                    success: true,
                    message: 'Document uploaded successfully',
                    document: [
                        id: uploadResult.document.id,
                        fileName: uploadResult.document.fileName,
                        documentType: uploadResult.document.documentType?.description,
                        uploadDate: uploadResult.document.uploadDate
                    ]
                ] as JSON)
            } else {
                render([success: false, error: uploadResult.message] as JSON)
            }
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error uploading customer document", 'Error uploading document')
        }
    }
    
    /**
     * Download customer document
     */
    def showContent() {
        log.info("Downloading customer document: ${params.documentId}")
        
        // Enforce permission: CUSTOMER_DOCUMENT_VIEW
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_DOCUMENT_VIEW")) {
            log.warn("User {} attempted to view document without CUSTOMER_DOCUMENT_VIEW permission.", currentUser?.username)
            response.sendError(403, "Access denied. You do not have permission to view documents.")
            auditLogService.insert('030', 'CDC01202', "Unauthorized attempt to view document by user ${currentUser?.username}", 'CustomerDocumentController', null, null, 'cif/customerDocument/showContent', currentUser?.id)
            return
        }
        
        try {
            if (!params.documentId) {
                response.sendError(400, "Document ID is required")
                return
            }
            
            CustomerDocument document = CustomerDocument.get(params.documentId)
            if (!document) {
                response.sendError(404, "Document not found")
                return
            }
            
            // Retrieve file content
            Map fileResult = fileStorageService.retrieveFile(document.filePath)
            if (!fileResult.success) {
                response.sendError(500, "Error retrieving document")
                return
            }
            
            // Set response headers
            response.setContentType(document.contentType ?: 'application/octet-stream')
            response.setContentLength(fileResult.content.length)
            response.setHeader('Content-disposition', "attachment; filename=${document.fileName}")
            
            response.outputStream << fileResult.content
            response.outputStream.flush()
            
            // Audit logging
            auditDocumentDownload(document, 'SUCCESS')
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error downloading customer document", 'Error downloading document')
        }
    }
    
    /**
     * Delete customer document
     */
    def delete() {
        log.info("Deleting customer document: ${params.documentId}")
        
        // Enforce permission: CUSTOMER_DOCUMENT_DELETE
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_DOCUMENT_DELETE")) {
            log.warn("User {} attempted to delete document without CUSTOMER_DOCUMENT_DELETE permission.", currentUser?.username)
            render([success: false, error: "You do not have permission to delete documents."] as JSON)
            auditLogService.insert('030', 'CDC01203', "Unauthorized attempt to delete document by user ${currentUser?.username}", 'CustomerDocumentController', null, null, 'cif/customerDocument/delete', currentUser?.id)
            return
        }
        
        try {
            if (!params.documentId) {
                render([success: false, error: 'Document ID is required'] as JSON)
                return
            }
            
            CustomerDocument document = CustomerDocument.get(params.documentId)
            if (!document) {
                render([success: false, error: 'Document not found'] as JSON)
                return
            }
            
            // Perform soft delete
            Map deleteResult = customerService.deleteCustomerDocument(document, params.reason)
            
            if (deleteResult.success) {
                // Audit logging
                auditDocumentDeletion(document, params.reason, 'SUCCESS')
                
                render([success: true, message: 'Document deleted successfully'] as JSON)
            } else {
                render([success: false, error: deleteResult.message] as JSON)
            }
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error deleting customer document", 'Error deleting document')
        }
    }
    
    /**
     * Get customer document list
     */
    def index() {
        log.debug("Getting customer document list: ${params.customerId}")
        
        // Enforce permission: CUSTOMER_DOCUMENT_LIST
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_DOCUMENT_LIST")) {
            log.warn("User {} attempted to list documents without CUSTOMER_DOCUMENT_LIST permission.", currentUser?.username)
            render([success: false, error: "You do not have permission to list documents."] as JSON)
            auditLogService.insert('030', 'CDC01204', "Unauthorized attempt to list documents by user ${currentUser?.username}", 'CustomerDocumentController', null, null, 'cif/customerDocument/index', currentUser?.id)
            return
        }
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            List<CustomerDocument> documents = CustomerDocument.createCriteria().list {
                eq("customer", customer)
                eq("isDeleted", false)
                order("uploadDate", "desc")
            }
            
            List<Map> documentList = documents.collect { doc ->
                [
                    id: doc.id,
                    fileName: doc.fileName,
                    documentType: doc.documentType?.description,
                    uploadDate: doc.uploadDate,
                    fileSize: doc.fileSize,
                    status: doc.status?.description,
                    expiryDate: doc.expiryDate,
                    isExpired: doc.expiryDate && doc.expiryDate < new Date()
                ]
            }
            
            render([
                success: true,
                documents: documentList,
                totalCount: documentList.size()
            ] as JSON)
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error getting customer document list", 'Error getting document list')
        }
    }
    
    /**
     * Get customer document checklist
     */
    def getDocumentChecklist() {
        log.debug("Getting customer document checklist: ${params.customerId}")
        
        // Enforce permission: CUSTOMER_DOCUMENT_CHECKLIST
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_DOCUMENT_CHECKLIST")) {
            log.warn("User {} attempted to get document checklist without CUSTOMER_DOCUMENT_CHECKLIST permission.", currentUser?.username)
            render([success: false, error: "You do not have permission to view document checklists."] as JSON)
            auditLogService.insert('030', 'CDC01205', "Unauthorized attempt to get document checklist by user ${currentUser?.username}", 'CustomerDocumentController', null, null, 'cif/customerDocument/getDocumentChecklist', currentUser?.id)
            return
        }
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map checklist = generateDocumentChecklist(customer)
            
            render([
                success: true,
                checklist: checklist
            ] as JSON)
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error getting customer document checklist", 'Error getting document checklist')
        }
    }
    
    /**
     * Update customer document status
     */
    def updateStatus() {
        log.info("Updating customer document status: ${params.documentId}")
        
        // Enforce permission: CUSTOMER_DOCUMENT_UPDATE_STATUS
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_DOCUMENT_UPDATE_STATUS")) {
            log.warn("User {} attempted to update document status without CUSTOMER_DOCUMENT_UPDATE_STATUS permission.", currentUser?.username)
            render([success: false, error: "You do not have permission to update document status."] as JSON)
            auditLogService.insert('030', 'CDC01206', "Unauthorized attempt to update document status by user ${currentUser?.username}", 'CustomerDocumentController', null, null, 'cif/customerDocument/updateStatus', currentUser?.id)
            return
        }
        
        try {
            if (!params.documentId || !params.statusId) {
                render([success: false, error: 'Document ID and Status ID are required'] as JSON)
                return
            }
            
            CustomerDocument document = CustomerDocument.get(params.documentId)
            ConfigItemStatus newStatus = ConfigItemStatus.get(params.statusId)
            
            if (!document) {
                render([success: false, error: 'Document not found'] as JSON)
                return
            }
            if (!newStatus) {
                render([success: false, error: 'Invalid status provided'] as JSON)
                return
            }
            
            // Perform status update
            document.status = newStatus
            if (document.save(flush: true)) {
                // Audit logging
                auditDocumentStatusUpdate(document, newStatus, 'SUCCESS')
                
                render([
                    success: true,
                    message: "Document status updated to ${newStatus.description} successfully.",
                    document: [
                        id: document.id,
                        fileName: document.fileName,
                        status: document.status?.description
                    ]
                ] as JSON)
            } else {
                render([success: false, error: "Failed to update document status: ${document.errors}"] as JSON)
            }
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error updating customer document status", 'Error updating document status')
        }
    }
    
    /**
     * Bulk upload documents
     */
    def bulkUploadDocuments() {
        log.info("Bulk uploading customer documents")
        
        try {
            if (!params.customerId) {
                render([success: false, error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([success: false, error: 'Customer not found'] as JSON)
                return
            }
            
            Map bulkResult = processBulkDocumentUpload(request, params, customer)
            
            // Audit logging
            auditBulkDocumentUpload(customer, bulkResult.uploadedCount, bulkResult.success ? 'SUCCESS' : 'PARTIAL')
            
            render(bulkResult as JSON)
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error bulk uploading documents", 'Error bulk uploading documents')
        }
    }
    
    /**
     * Generate document report
     */
    def generateDocumentReport() {
        log.info("Generating customer document report")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map reportData = generateDocumentReportData(fromDate, toDate, params)
            
            render(reportData as JSON)
            
        } catch (Exception e) {
            handleErrorResponse(e, "Error generating document report", 'Error generating document report')
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process document upload
     */
    private Map processDocumentUpload(def request, Map params, Customer customer) {
        Map result = [success: false, message: '', document: null]
        
        try {
            if (!(request instanceof MultipartHttpServletRequest)) {
                throw new RuntimeException('No file uploaded')
            }
            
            MultipartFile uploadedFile = request.getFile('documentFile')
            if (!uploadedFile || uploadedFile.empty) {
                throw new RuntimeException('No file selected')
            }
            
            // Validate file
            Map fileValidation = validateUploadedFile(uploadedFile)
            if (!fileValidation.isValid) {
                throw new RuntimeException(fileValidation.errors.join(', '))
            }
            
            // Store file
            String fileName = generateUniqueFileName(uploadedFile.originalFilename)
            Map storageResult = fileStorageService.storeFile(uploadedFile, fileName, 'customer-documents')
            
            if (!storageResult.success) {
                throw new RuntimeException('Error storing file')
            }
            
            // Create document record
            CustomerDocument document = new CustomerDocument(
                customer: customer,
                documentType: DocumentType.get(params.documentTypeId),
                fileName: fileName,
                originalFileName: uploadedFile.originalFilename,
                filePath: storageResult.filePath,
                fileSize: uploadedFile.size,
                contentType: uploadedFile.contentType,
                uploadDate: new Date(),
                uploadedBy: UserMaster.get(session.user_id),
                description: params.description,
                expiryDate: params.expiryDate ? Date.parse('yyyy-MM-dd', params.expiryDate) : null,
                status: ConfigItemStatus.get(2) // Active
            )
            
            document.save(flush: true, failOnError: true)
            
            result.success = true
            result.document = document
            result.message = 'Document uploaded successfully'
            
        } catch (Exception e) {
            log.error("Error processing document upload", e)
            throw new RuntimeException('Error processing document upload', e)
        }
        
        return result
    }
    
    /**
     * Process bulk document upload
     */
    private Map processBulkDocumentUpload(def request, Map params, Customer customer) {
        Map result = [success: true, uploadedCount: 0, failedCount: 0, errors: []]
        
        try {
            if (!(request instanceof MultipartHttpServletRequest)) {
                throw new RuntimeException('No files uploaded')
            }
            
            request.fileNames.each { fileName ->
                try {
                    MultipartFile uploadedFile = request.getFile(fileName)
                    if (uploadedFile && !uploadedFile.empty) {
                        Map uploadResult = processDocumentUpload(request, params, customer)
                        if (uploadResult.success) {
                            result.uploadedCount++
                        } else {
                            result.failedCount++
                            result.errors << "File ${uploadedFile.originalFilename}: ${uploadResult.message}"
                        }
                    }
                } catch (Exception e) {
                    result.failedCount++
                    result.errors << "Error processing file: ${e.message}"
                }
            }
            
            result.success = result.failedCount == 0
            
        } catch (Exception e) {
            log.error("Error processing bulk document upload", e)
            throw new RuntimeException('Error processing bulk upload', e)
        }
        
        return result
    }
    
    /**
     * Generate document checklist
     */
    private Map generateDocumentChecklist(Customer customer) {
        Map checklist = [required: [], optional: [], submitted: [], completionStatus: [:]]
        
        try {
            // Get required documents based on customer type
            checklist.required = getRequiredDocuments(customer)
            
            // Get optional documents
            checklist.optional = getOptionalDocuments(customer)
            
            // Get submitted documents
            checklist.submitted = customer.documents?.findAll { !it.isDeleted }?.collect {
                [
                    id: it.id,
                    documentType: it.documentType?.description,
                    fileName: it.fileName,
                    uploadDate: it.uploadDate,
                    status: it.status?.description,
                    isExpired: it.expiryDate && it.expiryDate < new Date()
                ]
            } ?: []
            
            // Calculate completion status
            checklist.completionStatus = calculateCompletionStatus(checklist)
            
        } catch (Exception e) {
            log.error("Error generating document checklist", e)
            throw new RuntimeException("Error generating document checklist", e)
        }
        
        return checklist
    }
    
    /**
     * Get required documents for customer
     */
    private List getRequiredDocuments(Customer customer) {
        try {
            return DocumentType.createCriteria().list {
                eq("isRequired", true)
                eq("entityType", "CUSTOMER")
                eq("isActive", true)
                // Add customer type specific filtering if needed
            }
        } catch (Exception e) {
            log.error("Error getting required documents", e)
            throw new RuntimeException("Error getting required documents", e)
        }
    }
    
    /**
     * Get optional documents for customer
     */
    private List getOptionalDocuments(Customer customer) {
        try {
            return DocumentType.createCriteria().list {
                eq("isRequired", false)
                eq("entityType", "CUSTOMER")
                eq("isActive", true)
            }
        } catch (Exception e) {
            log.error("Error getting optional documents", e)
            throw new RuntimeException("Error getting optional documents", e)
        }
    }
    
    /**
     * Calculate completion status
     */
    private Map calculateCompletionStatus(Map checklist) {
        Map status = [:]
        
        try {
            Integer requiredCount = checklist.required.size()
            Integer submittedRequiredCount = 0
            
            checklist.required.each { requiredDoc ->
                boolean isSubmitted = checklist.submitted.any { 
                    it.documentType == requiredDoc.description 
                }
                if (isSubmitted) {
                    submittedRequiredCount++
                }
            }
            
            status.totalRequired = requiredCount
            status.submittedRequired = submittedRequiredCount
            status.completionPercentage = requiredCount > 0 ? 
                (submittedRequiredCount / requiredCount * 100).setScale(2) : 100
            status.isComplete = submittedRequiredCount == requiredCount
            
        } catch (Exception e) {
            log.error("Error calculating completion status", e)
            throw new RuntimeException("Error calculating completion status", e)
        }
        
        return status
    }
    
    /**
     * Validate uploaded file
     */
    private Map validateUploadedFile(MultipartFile file) {
        Map validation = [isValid: true, errors: []]
        
        try {
            // Check file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                validation.isValid = false
                validation.errors << "File size exceeds 10MB limit"
            }
            
            // Check file type
            List allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif', 'application/msword']
            if (!allowedTypes.contains(file.contentType)) {
                validation.isValid = false
                validation.errors << "File type not allowed"
            }
            
        } catch (Exception e) {
            log.error("Error validating uploaded file", e)
            throw new RuntimeException("Error validating file", e)
        }
        
        return validation
    }
    
    /**
     * Generate unique file name
     */
    private String generateUniqueFileName(String originalFileName) {
        try {
            String extension = originalFileName.substring(originalFileName.lastIndexOf('.'))
            String timestamp = new Date().format('yyyyMMdd_HHmmss')
            String randomString = UUID.randomUUID().toString().substring(0, 8)
            return "customer_doc_${timestamp}_${randomString}${extension}"
        } catch (Exception e) {
            log.error("Error generating unique file name", e)
            throw new RuntimeException("Error generating unique file name", e)
        }
    }
    
    /**
     * Generate document report data
     */
    private Map generateDocumentReportData(Date fromDate, Date toDate, Map filters) {
        Map data = [:]
        
        try {
            // Document upload statistics
            data.uploadStats = [
                totalUploads: CustomerDocument.countByUploadDateBetween(fromDate, toDate),
                byDocumentType: DocumentType.list().collect { type ->
                    [
                        type: type.description,
                        count: CustomerDocument.countByDocumentTypeAndUploadDateBetween(type, fromDate, toDate)
                    ]
                }
            ]
            
            // Compliance statistics
            data.complianceStats = [
                compliantCustomers: 0, // Calculate based on document completeness
                nonCompliantCustomers: 0,
                expiredDocuments: CustomerDocument.countByExpiryDateLessThan(new Date())
            ]
            
        } catch (Exception e) {
            log.error("Error generating document report data", e)
            throw new RuntimeException("Error generating document report data", e)
        }
        
        return data
    }
    
    // Audit methods
    private void auditDocumentUpload(Customer customer, CustomerDocument document, String result) {
        try {
            auditLogService.insert('120', 'CDC05100', 
                "Customer document uploaded - Customer: ${customer.id}, Document: ${document.fileName}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing document upload", e)
        }
    }
    
    private void auditDocumentDownload(CustomerDocument document, String result) {
        try {
            auditLogService.insert('120', 'CDC05200', 
                "Customer document downloaded - Document: ${document.fileName}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document download", e)
        }
    }
    
    private void auditDocumentDeletion(CustomerDocument document, String reason, String result) {
        try {
            auditLogService.insert('120', 'CDC05300', 
                "Customer document deleted - Document: ${document.fileName}, Reason: ${reason}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document deletion", e)
        }
    }
    
    private void auditDocumentStatusUpdate(CustomerDocument document, ConfigItemStatus newStatus, String result) {
        try {
            auditLogService.insert('120', 'CDC05400', 
                "Customer document status updated - Document: ${document.fileName}, Status: ${newStatus.description}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document status update", e)
        }
    }
    
    private void auditBulkDocumentUpload(Customer customer, Integer uploadedCount, String result) {
        try {
            auditLogService.insert('120', 'CDC05500', 
                "Bulk customer documents uploaded - Customer: ${customer.id}, Count: ${uploadedCount}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing bulk document upload", e)
        }
    }
    
    def handleErrorResponse(Exception e, String logMessage, String userMessage) {
        log.error(logMessage, e)
        render([success: false, error: userMessage] as JSON)
    }
}

package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import org.icbs.admin.CustomerGroup
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.grails.web.json.JSONObject
import java.text.DateFormat
import java.text.SimpleDateFormat
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import org.icbs.security.PermissionService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Registration Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer registration and creation operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerRegistrationController {
    
    static namespace = 'api/v1'
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def policyService
    def auditLogService
    def permissionService
    
    static allowedMethods = [
        create: "GET",
        createCustomer: "POST",
        checkCustomerDuplicates: "POST",
        validateCustomerOtherDetails: "POST",
        validateCustomerContactInfo: "POST"
    ]
    
    // =====================================================
    // CUSTOMER REGISTRATION OPERATIONS
    // =====================================================
    
    /**
     * Create new customer form
     */
    def create() {
        log.info("Creating new customer form")
        
        // Enforce permission: CUSTOMER_CREATE
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_CREATE")) {
            log.warn("User {} attempted to access customer creation without CUSTOMER_CREATE permission.", currentUser?.username)
            flash.error = "You do not have permission to create customers."
            auditLogService.insert('030', 'CRC01101', "Unauthorized access to customer creation by user ${currentUser?.username}", 'CustomerRegistrationController', null, null, 'cif/create', currentUser?.id)
            redirect(controller: 'home', action: 'unauthorized')
            return
        }

        try {
            session["customerpagevalidator"] = "create"
            
            if (!params.type?.id) {
                render(view: 'create', model: [
                    'customerInstance': new Customer(params),
                    'firstCreate': true
                ])
            } else {
                respond new Customer(params)
            }
            
            // Audit logging
            auditLogService.insert('120', 'CRC01100', 
                "Customer registration form accessed", 
                'CustomerRegistrationController', null, null, 'cif/create', session.user_id)
            
        } catch (Exception e) {
            log.error("Error creating customer form", e)
            flash.message = 'Error accessing customer registration form|error|alert'
            redirect(controller: 'customer', action: 'index')
        }
    }
    
    /**
     * Save new customer
     */
    def createCustomer(Customer customerInstance) {
        log.info("Saving new customer: ${customerInstance?.name1}")

        // Enforce permission: CUSTOMER_CREATE
        UserMaster currentUser = UserMaster.get(session.user_id)
        if (!permissionService.hasPermission(currentUser, "CUSTOMER_CREATE")) {
            log.warn("User {} attempted to save customer without CUSTOMER_CREATE permission.", currentUser?.username)
            flash.error = "You do not have permission to create customers."
            auditLogService.insert('030', 'CRC01102', "Unauthorized attempt to save customer by user ${currentUser?.username}", 'CustomerRegistrationController', null, null, 'cif/createCustomer', currentUser?.id)
            redirect(controller: 'home', action: 'unauthorized')
            return
        }

        try {
            // NEW: Bind and validate the CustomerRegistrationCommand
            CustomerRegistrationCommand cmd = new CustomerRegistrationCommand()
            cmd.properties = params

            if (cmd.validate()) {
                // 1. Process file attachments (moved inside successful validation, if still using params)
                Map attachmentResult = processFileAttachments(request, params)
                if (!attachmentResult.success) {
                    flash.message = 'Error processing file attachments|error|alert'
                    respond customerInstance, view: 'create'
                    return
                }

                // 2. Set system fields (params are still used here, may need to map from cmd later)
                setSystemFields(params)
                
                // 3. Set default values (params are still used here, may need to map from cmd later)
                setDefaultValues(params)
                
                // 4. Map validated data from command object to customerInstance
                // This part requires careful mapping as `customerInstance` is directly bound by Grails
                // For complex nested objects, manual mapping or a dedicated mapper service might be needed.
                // For simplicity here, we assume customerInstance binding handles basic fields and
                // nested command objects will be processed in the service layer.
                // It's better to pass the command object to the service and let the service handle persistence.
                // For now, let's just make sure customerInstance has the basic data.
                // The saveCustomer method in customerService might need to be updated to accept the command object.
                
                // Replacing unifiedValidationService.validateCustomerRegistration(params)
                // The command object's validate() method now handles this.

                // 5. Save customer using the validated command object
                // Assuming customerService.saveCustomer can now accept a command object or params
                Map saveResult = customerService.saveCustomer(customerInstance, params) // Still passing params for now
                
                if (saveResult.success) {
                    // 6. Process additional data
                    processAdditionalCustomerData()
                    
                    // 7. Audit logging
                    auditCustomerRegistration(saveResult.customer, 'SUCCESS')
                    
                    request.withFormat {
                        form multipartForm {
                            flash.message = 'Customer registered successfully|success|alert'
                            redirect(action: "show", id: saveResult.customer.id)
                        }
                        '*' { respond saveResult.customer, [status: CREATED] }
                    }
                } else {
                    // If service returns errors, assign them back to customerInstance
                    customerInstance.errors = saveResult.errors
                    respond customerInstance.errors, view: 'create'
                }
            } else {
                // Validation failed on the command object
                customerInstance.errors = cmd.errors // Assign command object errors to customerInstance for rendering
                respond customerInstance.errors, view: 'create'
            }
            
        } catch (Exception e) {
            log.error("Error saving customer", e)
            flash.message = 'Error saving customer|error|alert'
            // If an exception occurs, ensure errors are present for rendering
            respond customerInstance.errors, view: 'create'
        }
    }
    
    /**
     * Customer verification AJAX
     */
    def checkCustomerDuplicates(customerVerificationCommand cmd) {
        log.debug("Processing customer verification AJAX")
        
        try {
            JSONObject jsonObject = new JSONObject()
            
            if (cmd.hasErrors()) {
                jsonObject = jsonObject.put("html", 
                    g.render(template: 'form/customer/customerverification/private', 
                    model: [customerInstance: cmd, onsubmit: "true", isVerified: "false"]))
            } else {
                // Check for duplicates
                List duplicateList = checkForDuplicateCustomers(cmd)
                
                if (duplicateList) {
                    jsonObject = jsonObject.put("html", 
                        g.render(template: 'form/customer/customerverification/private', 
                        model: [customerInstance: cmd, duplicateList: duplicateList, 
                               onsubmit: "true", isVerified: "false"]))
                } else {
                    jsonObject = jsonObject.put("html", 
                        g.render(template: 'form/customer/customerverification/private', 
                        model: [customerInstance: cmd, duplicateList: duplicateList, 
                               onsubmit: "true", isVerified: "true"]))
                }
            }
            
            render jsonObject
            
        } catch (Exception e) {
            log.error("Error in customer verification AJAX", e)
            render([error: 'Error processing verification'] as JSON)
        }
    }
    
    /**
     * Customer other details validation AJAX
     */
    def validateCustomerOtherDetails(customerOtherDetailsCommand cmd) {
        log.debug("Processing customer other details validation AJAX")
        
        try {
            render(template: 'form/customer/othercustomerinfo/otherCustomerInfo', 
                  model: ['customerInstance': cmd]) as JSON
        } catch (Exception e) {
            log.error("Error in customer other details validation AJAX", e)
            render([error: 'Error processing validation'] as JSON)
        }
    }
    
    /**
     * Customer contact information validation AJAX
     */
    def validateCustomerContactInfo(customerContactInformationCommand cmd) {
        log.debug("Processing customer contact information validation AJAX")
        
        try {
            render(template: 'form/customer/contactinformation/contactInfo', 
                  model: ['customerInstance': cmd]) as JSON
        } catch (Exception e) {
            log.error("Error in customer contact information validation AJAX", e)
            render([error: 'Error processing validation'] as JSON)
        }
    }
    
    /**
     * Validate customer registration data
     */
    def validateRegistrationData() {
        log.debug("Validating customer registration data")
        
        try {
            Map validationResult = unifiedValidationService.validateCustomerRegistration(params)
            
            render([
                isValid: validationResult.isValid,
                errors: validationResult.errors,
                warnings: validationResult.warnings
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating registration data", e)
            render([isValid: false, errors: ['Error validating registration data']] as JSON)
        }
    }
    
    /**
     * Check customer eligibility
     */
    def checkCustomerEligibility() {
        log.debug("Checking customer eligibility")
        
        try {
            Map eligibilityResult = customerService.checkCustomerEligibility(params)
            
            render([
                isEligible: eligibilityResult.isEligible,
                reasons: eligibilityResult.reasons,
                requirements: eligibilityResult.requirements
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error checking customer eligibility", e)
            render([isEligible: false, reasons: ['Error checking eligibility']] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process file attachments
     */
    private Map processFileAttachments(def request, Map params) {
        Map result = [success: true, errors: []]
        
        try {
            if (request instanceof MultipartHttpServletRequest) {
                request.fileNames.each { fileName ->
                    def uploadedFile = request.getFile(fileName)
                    String subscript = (fileName.split("\\.")[0])
                    
                    if (uploadedFile.getSize() < 1) {
                        if (!params[subscript + ".id"]) {
                            params.remove(subscript + '.fileName')
                        }
                    } else {
                        params[subscript + ".fileName"] = uploadedFile.getOriginalFilename()
                        params[subscript + ".fileType"] = uploadedFile.getContentType()
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing file attachments", e)
            result.success = false
            result.errors << "Error processing file attachments"
        }
        
        return result
    }
    
    /**
     * Set system fields
     */
    private void setSystemFields(Map params) {
        try {
            UserMaster currentUser = UserMaster.get(session.user_id)
            
            params.branch = currentUser?.branch?.id
            params.createdBy = currentUser
            params.lastUpdatedBy = currentUser
            
        } catch (Exception e) {
            log.error("Error setting system fields", e)
        }
    }
    
    /**
     * Set default values
     */
    private void setDefaultValues(Map params) {
        try {
            if (!params.dosriCode) {
                params.dosriCode = CustomerDosriCode.get(1)
            }
            
            // Set other default values as needed
            
        } catch (Exception e) {
            log.error("Error setting default values", e)
        }
    }
    
    /**
     * Check for duplicate customers
     */
    private List checkForDuplicateCustomers(customerVerificationCommand cmd) {
        try {
            return Customer.createCriteria().list {
                and {
                    eq("name1", cmd.name1)
                    eq("name2", cmd.name2)
                    eq("birthDate", cmd.birthDate)
                }
                maxResults(10)
            }
            
        } catch (Exception e) {
            log.error("Error checking for duplicate customers", e)
            return []
        }
    }
    
    /**
     * Process additional customer data
     */
    private void processAdditionalCustomerData() {
        try {
            // Process contacts, addresses, beneficiaries, etc.
            // This would integrate with other services
            
        } catch (Exception e) {
            log.error("Error processing additional customer data", e)
        }
    }
    
    /**
     * Audit customer registration
     */
    private void auditCustomerRegistration(Customer customer, String result) {
        try {
            auditLogService.insert('120', 'CRC01000', 
                "Customer registered - Name: ${customer.name1} ${customer.name2}, Result: ${result}", 
                'CustomerRegistrationController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing customer registration", e)
        }
    }
}

// =====================================================
// COMMAND CLASSES (Moved from original controller)
// =====================================================

@grails.validation.Validateable
class customerVerificationCommand {
    CustomerType type
    int id
    CustomerGroup group
    String name1
    String name2
    String name3
    String name4
    Lov name5
    Lov title
    Gender gender
    Lov civilStatus
    Date birthDate
    String birthPlace
    Town custBirthPlace
    
    static constraints = {
        group nullable: false
        importFrom Customer
    }
}

@grails.validation.Validateable
class customerOtherDetailsCommand {
    CustomerType type
    String displayName
    ResidentType customerCode1
    RiskType customerCode2
    FirmSize customerCode3
    String sourceOfIncome
    Lov nationality
    CustomerDosriCode dosriCode
    String sssNo
    String gisNo
    String tinNo
    String passportNo
    String pepDescription
    String amla
    Integer noOfDependent
    String motherMaidenName
    String fatherName
    String spouseLastName
    String spouseFirstName
    String spouseMiddleName
    Date spouseBirthDate
    String spouseContactNo
    Religion religion
    
    static constraints = {
        importFrom Customer
    }
}

@grails.validation.Validateable
class customerContactsCommand {
    List<Contact> contacts = [].withLazyDefault { new Contact() }
    
    // Reusable closure for validating hasMany collections in command objects
    def static validateCollection = { collection, fieldName, errors ->
        if (collection) {
            collection.eachWithIndex { item, i ->
                if (item && !item.validate()) {
                    item.errors.allErrors.each { error ->
                        errors.rejectValue(
                            "${fieldName}[${i}].${error.field}",
                            error.getCode(),
                            error.getArguments(),
                            error.getDefaultMessage()
                        )
                    }
                }
            }
        }
    }

    static constraints = {
        contacts validator: validateCollection.curry('contacts')
    }
}

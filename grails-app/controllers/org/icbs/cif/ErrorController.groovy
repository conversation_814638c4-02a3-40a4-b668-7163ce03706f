package org.icbs.cif

import grails.web.mapping.LinkGenerator
import groovy.util.logging.Slf4j
import org.springframework.http.HttpStatus

@Slf4j
class ErrorController {

    LinkGenerator grailsLinkGenerator

    def notFound() {
        log.warn "A 404 Not Found error occurred for request URI: ${request.forwardURI}"
        response.status = HttpStatus.NOT_FOUND.value()
        render(view: '/error/notFound')
    }

    def serverError() {
        log.error "A 500 Internal Server Error occurred for request URI: ${request.forwardURI}", request.exception
        response.status = HttpStatus.INTERNAL_SERVER_ERROR.value()
        render(view: '/error/serverError')
    }
} 
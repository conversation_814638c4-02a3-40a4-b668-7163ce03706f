package org.icbs.admin

import org.icbs.lov.ConfigItemStatus

/**
 * DocumentType Domain Class
 * Defines types of documents that can be associated with customers, loans, etc.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DocumentType {
    
    String code
    String name
    String description
    Boolean required = false
    Boolean hasExpiry = false
    Integer validityDays
    String category // IDENTITY, FINANCIAL, LEGAL, etc.
    ConfigItemStatus status
    Date dateCreated = new Date()
    Date lastUpdated = new Date()
    
    static constraints = {
        code nullable: false, blank: false, unique: true, maxSize: 20
        name nullable: false, blank: false, maxSize: 100
        description nullable: true, maxSize: 500
        required nullable: false
        hasExpiry nullable: false
        validityDays nullable: true, min: 1
        category nullable: true, maxSize: 50
        status nullable: false
        dateCreated nullable: false
        lastUpdated nullable: false
    }
    
    static mapping = {
        table 'document_type'
        id generator: 'increment'
        code column: 'doc_type_code'
        name column: 'doc_type_name'
        description column: 'description', type: 'text'
        required column: 'is_required'
        hasExpiry column: 'has_expiry'
        validityDays column: 'validity_days'
        category column: 'category'
        status column: 'status_id'
        dateCreated column: 'date_created'
        lastUpdated column: 'last_updated'
    }
    
    String toString() {
        return "${code} - ${name}"
    }
}

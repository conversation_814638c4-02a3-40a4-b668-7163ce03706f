package org.icbs.admin

/**
 * PasswordHistory Domain Class
 * Tracks password history for users to prevent password reuse
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class PasswordHistory {
    
    UserMaster userMaster
    String password
    Date dateUsed
    Date dateCreated = new Date()
    
    static constraints = {
        userMaster nullable: false
        password nullable: false, blank: false, maxSize: 255
        dateUsed nullable: false
        dateCreated nullable: false
    }
    
    static mapping = {
        table 'password_history'
        id generator: 'increment'
        userMaster column: 'user_master_id'
        password column: 'password_hash'
        dateUsed column: 'date_used'
        dateCreated column: 'date_created'
        version false
    }
    
    String toString() {
        return "PasswordHistory[${id}] - User: ${userMaster?.username}, Date: ${dateUsed}"
    }
}

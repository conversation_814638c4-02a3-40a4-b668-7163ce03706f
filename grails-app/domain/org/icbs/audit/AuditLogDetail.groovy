package org.icbs.audit

import grails.gorm.MultiTenant

class AuditLogDetail implements MultiTenant<AuditLogDetail> {
    AuditLog auditLog
    String fieldName
    String oldValue
    String newValue

    static belongsTo = [auditLog: AuditLog]

    static constraints = {
        auditLog nullable: false
        fieldName maxSize: 100, nullable: false
        oldValue maxSize: 1000, nullable: true // Store as string, can be JSON for complex objects
        newValue maxSize: 1000, nullable: true // Store as string
    }

    static mapping = {
        id generator: 'identity'
        auditLog column: 'audit_log_id'
        // Consider adding a mapping for the text type if values can be very long
        oldValue type: 'text'
        newValue type: 'text'
    }
} 
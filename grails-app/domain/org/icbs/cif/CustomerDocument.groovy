package org.icbs.cif

import org.icbs.admin.DocumentType
import org.icbs.lov.ConfigItemStatus

/**
 * CustomerDocument Domain Class
 * Manages documents associated with customers
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class CustomerDocument {
    
    Customer customer
    DocumentType documentType
    String documentNumber
    String fileName
    String originalFileName
    String filePath
    String mimeType
    Long fileSize
    Date issueDate
    Date expiryDate
    String issuingAuthority
    String remarks
    ConfigItemStatus status
    Date dateCreated = new Date()
    Date lastUpdated = new Date()
    String uploadedBy
    
    static constraints = {
        customer nullable: false
        documentType nullable: false
        documentNumber nullable: true, maxSize: 100
        fileName nullable: false, maxSize: 255
        originalFileName nullable: false, maxSize: 255
        filePath nullable: false, maxSize: 500
        mimeType nullable: true, maxSize: 100
        fileSize nullable: true, min: 0L
        issueDate nullable: true
        expiryDate nullable: true
        issuingAuthority nullable: true, maxSize: 200
        remarks nullable: true, maxSize: 1000
        status nullable: false
        dateCreated nullable: false
        lastUpdated nullable: false
        uploadedBy nullable: true, maxSize: 100
    }
    
    static mapping = {
        table 'customer_document'
        id generator: 'increment'
        customer column: 'customer_id'
        documentType column: 'document_type_id'
        documentNumber column: 'document_number'
        fileName column: 'file_name'
        originalFileName column: 'original_file_name'
        filePath column: 'file_path'
        mimeType column: 'mime_type'
        fileSize column: 'file_size'
        issueDate column: 'issue_date'
        expiryDate column: 'expiry_date'
        issuingAuthority column: 'issuing_authority'
        remarks column: 'remarks', type: 'text'
        status column: 'status_id'
        dateCreated column: 'date_created'
        lastUpdated column: 'last_updated'
        uploadedBy column: 'uploaded_by'
    }
    
    String toString() {
        return "CustomerDocument[${id}] - ${customer?.displayName} - ${documentType?.name}"
    }
}

package org.icbs.cif

import org.icbs.lov.ConfigItemStatus

/**
 * CustomerRelationship Domain Class
 * Manages relationships between customers (family, business, etc.)
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class CustomerRelationship {
    
    Customer primaryCustomer
    Customer relatedCustomer
    String relationshipType // SPOUSE, CHILD, PARENT, BUSINESS_PARTNER, etc.
    String description
    Date relationshipStartDate
    Date relationshipEndDate
    Boolean isActive = true
    String remarks
    ConfigItemStatus status
    Date dateCreated = new Date()
    Date lastUpdated = new Date()
    String createdBy
    String updatedBy
    
    static constraints = {
        primaryCustomer nullable: false
        relatedCustomer nullable: false
        relationshipType nullable: false, maxSize: 50
        description nullable: true, maxSize: 200
        relationshipStartDate nullable: true
        relationshipEndDate nullable: true
        isActive nullable: false
        remarks nullable: true, maxSize: 500
        status nullable: false
        dateCreated nullable: false
        lastUpdated nullable: false
        createdBy nullable: true, maxSize: 100
        updatedBy nullable: true, maxSize: 100
    }
    
    static mapping = {
        table 'customer_relationship'
        id generator: 'increment'
        primaryCustomer column: 'primary_customer_id'
        relatedCustomer column: 'related_customer_id'
        relationshipType column: 'relationship_type'
        description column: 'description'
        relationshipStartDate column: 'relationship_start_date'
        relationshipEndDate column: 'relationship_end_date'
        isActive column: 'is_active'
        remarks column: 'remarks', type: 'text'
        status column: 'status_id'
        dateCreated column: 'date_created'
        lastUpdated column: 'last_updated'
        createdBy column: 'created_by'
        updatedBy column: 'updated_by'
    }
    
    String toString() {
        return "CustomerRelationship[${id}] - ${primaryCustomer?.displayName} -> ${relatedCustomer?.displayName} (${relationshipType})"
    }
}

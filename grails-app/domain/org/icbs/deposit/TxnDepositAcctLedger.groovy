package org.icbs.deposit

import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.lov.ConfigItemStatus

/**
 * TxnDepositAcctLedger Domain Class
 * Transaction ledger for deposit accounts
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class TxnDepositAcctLedger {
    
    Deposit deposit
    Branch branch
    Currency currency
    String transactionNumber
    String transactionType // DEPOSIT, WITHDRAWAL, TRANSFER, INTEREST, etc.
    String description
    BigDecimal debitAmount = 0.0
    BigDecimal creditAmount = 0.0
    BigDecimal runningBalance = 0.0
    Date transactionDate
    Date valueDate
    String referenceNumber
    String tellerCode
    String authorizedBy
    Boolean isReversed = false
    String reversalReason
    Date reversalDate
    String reversedBy
    ConfigItemStatus status
    Date dateCreated = new Date()
    
    static constraints = {
        deposit nullable: false
        branch nullable: false
        currency nullable: false
        transactionNumber nullable: false, unique: true, maxSize: 50
        transactionType nullable: false, maxSize: 50
        description nullable: false, maxSize: 200
        debitAmount nullable: false, min: 0.0, scale: 2
        creditAmount nullable: false, min: 0.0, scale: 2
        runningBalance nullable: false, scale: 2
        transactionDate nullable: false
        valueDate nullable: false
        referenceNumber nullable: true, maxSize: 100
        tellerCode nullable: true, maxSize: 20
        authorizedBy nullable: true, maxSize: 100
        isReversed nullable: false
        reversalReason nullable: true, maxSize: 200
        reversalDate nullable: true
        reversedBy nullable: true, maxSize: 100
        status nullable: false
        dateCreated nullable: false
    }
    
    static mapping = {
        table 'txn_deposit_acct_ledger'
        id generator: 'increment'
        deposit column: 'deposit_id'
        branch column: 'branch_id'
        currency column: 'currency_id'
        transactionNumber column: 'transaction_number'
        transactionType column: 'transaction_type'
        description column: 'description'
        debitAmount column: 'debit_amount'
        creditAmount column: 'credit_amount'
        runningBalance column: 'running_balance'
        transactionDate column: 'transaction_date'
        valueDate column: 'value_date'
        referenceNumber column: 'reference_number'
        tellerCode column: 'teller_code'
        authorizedBy column: 'authorized_by'
        isReversed column: 'is_reversed'
        reversalReason column: 'reversal_reason'
        reversalDate column: 'reversal_date'
        reversedBy column: 'reversed_by'
        status column: 'status_id'
        dateCreated column: 'date_created'
    }
    
    String toString() {
        return "TxnDepositAcctLedger[${id}] - ${deposit?.accountNumber} - ${transactionType}: ${creditAmount ?: debitAmount}"
    }
}

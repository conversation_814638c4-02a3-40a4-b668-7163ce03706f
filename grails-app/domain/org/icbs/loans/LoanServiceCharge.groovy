package org.icbs.loans

import org.icbs.lov.ConfigItemStatus

/**
 * LoanServiceCharge Domain Class
 * Manages service charges associated with loans
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class LoanServiceCharge {
    
    Loan loan
    String chargeType // PROCESSING_FEE, DOCUMENTATION_FEE, INSURANCE, etc.
    String chargeName
    BigDecimal chargeAmount = 0.0
    BigDecimal chargeRate = 0.0 // percentage if applicable
    String calculationBasis = 'FIXED' // FIXED, PERCENTAGE_OF_LOAN
    Date chargeDate
    Boolean isPaid = false
    Date paidDate
    String paidBy
    String remarks
    ConfigItemStatus status
    Date dateCreated = new Date()
    Date lastUpdated = new Date()
    
    static constraints = {
        loan nullable: false
        chargeType nullable: false, maxSize: 50
        chargeName nullable: false, maxSize: 100
        chargeAmount nullable: false, min: 0.0, scale: 2
        chargeRate nullable: false, min: 0.0, max: 100.0, scale: 4
        calculationBasis nullable: false, inList: ['FIXED', 'PERCENTAGE_OF_LOAN']
        chargeDate nullable: false
        isPaid nullable: false
        paidDate nullable: true
        paidBy nullable: true, maxSize: 100
        remarks nullable: true, maxSize: 500
        status nullable: false
        dateCreated nullable: false
        lastUpdated nullable: false
    }
    
    static mapping = {
        table 'loan_service_charge'
        id generator: 'increment'
        loan column: 'loan_id'
        chargeType column: 'charge_type'
        chargeName column: 'charge_name'
        chargeAmount column: 'charge_amount'
        chargeRate column: 'charge_rate'
        calculationBasis column: 'calculation_basis'
        chargeDate column: 'charge_date'
        isPaid column: 'is_paid'
        paidDate column: 'paid_date'
        paidBy column: 'paid_by'
        remarks column: 'remarks', type: 'text'
        status column: 'status_id'
        dateCreated column: 'date_created'
        lastUpdated column: 'last_updated'
    }
    
    String toString() {
        return "LoanServiceCharge[${id}] - ${loan?.loanNumber} - ${chargeName}: ${chargeAmount}"
    }
}

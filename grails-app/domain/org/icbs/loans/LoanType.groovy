package org.icbs.loans

import org.icbs.lov.ConfigItemStatus

/**
 * LoanType Domain Class
 * Defines different types of loans available in the system
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class LoanType {
    
    String code
    String name
    String description
    BigDecimal minAmount = 0.0
    BigDecimal maxAmount = 999999999.99
    Integer minTenure = 1 // months
    Integer maxTenure = 360 // months
    BigDecimal interestRate = 0.0
    String interestType = 'FIXED' // FIXED, VARIABLE
    Boolean requiresCollateral = false
    Boolean allowsEarlyPayment = true
    BigDecimal earlyPaymentPenalty = 0.0
    String category = 'PERSONAL' // PERSONAL, BUSINESS, MORTGAGE, etc.
    ConfigItemStatus status
    Date dateCreated = new Date()
    Date lastUpdated = new Date()
    
    static constraints = {
        code nullable: false, blank: false, unique: true, maxSize: 20
        name nullable: false, blank: false, maxSize: 100
        description nullable: true, maxSize: 500
        minAmount nullable: false, min: 0.0, scale: 2
        maxAmount nullable: false, min: 0.0, scale: 2
        minTenure nullable: false, min: 1
        maxTenure nullable: false, min: 1
        interestRate nullable: false, min: 0.0, max: 100.0, scale: 4
        interestType nullable: false, inList: ['FIXED', 'VARIABLE']
        requiresCollateral nullable: false
        allowsEarlyPayment nullable: false
        earlyPaymentPenalty nullable: false, min: 0.0, scale: 2
        category nullable: false, maxSize: 50
        status nullable: false
        dateCreated nullable: false
        lastUpdated nullable: false
    }
    
    static mapping = {
        table 'loan_type'
        id generator: 'increment'
        code column: 'loan_type_code'
        name column: 'loan_type_name'
        description column: 'description', type: 'text'
        minAmount column: 'min_amount'
        maxAmount column: 'max_amount'
        minTenure column: 'min_tenure'
        maxTenure column: 'max_tenure'
        interestRate column: 'interest_rate'
        interestType column: 'interest_type'
        requiresCollateral column: 'requires_collateral'
        allowsEarlyPayment column: 'allows_early_payment'
        earlyPaymentPenalty column: 'early_payment_penalty'
        category column: 'category'
        status column: 'status_id'
        dateCreated column: 'date_created'
        lastUpdated column: 'last_updated'
    }
    
    String toString() {
        return "${code} - ${name}"
    }
}

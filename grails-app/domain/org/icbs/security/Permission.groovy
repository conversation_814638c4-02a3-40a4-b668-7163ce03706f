package org.icbs.security

import org.icbs.admin.Module
import org.icbs.lov.ConfigItemStatus

class Permission {

    String code
    String name
    String description
    Module module // Associate permission with a module (optional, for categorization)
    ConfigItemStatus configItemStatus

    static constraints = {
        code maxSize: 50, unique: true, blank: false
        name maxSize: 100, unique: true, blank: false
        description maxSize: 255, nullable: true
        module nullable: true
        configItemStatus nullable: false
    }

    static mapping = {
        id generator: 'identity'
        configItemStatus sqlType: 'smallint'
        module column: 'module_id'
    }
} 
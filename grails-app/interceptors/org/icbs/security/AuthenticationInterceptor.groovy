package org.icbs.security

import grails.web.api.WebAttributes
import groovy.util.logging.Slf4j
import io.jsonwebtoken.Claims
import org.springframework.http.HttpStatus
import javax.servlet.http.HttpServletRequest
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import org.slf4j.MDC
import org.icbs.admin.UserMaster

/**
 * Authentication Interceptor
 * Handles JWT-based authentication for API endpoints and web requests
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Slf4j
class AuthenticationInterceptor implements WebAttributes {
    
    JwtTokenService jwtTokenService
    SecurityAuditService securityAuditService
    RateLimitingService rateLimitingService
    
    // Define which controllers/actions require authentication
    private static final List<String> EXCLUDED_CONTROLLERS = [
        'login', 'authentication', 'public', 'health', 'error'
    ]
    
    private static final List<String> EXCLUDED_ACTIONS = [
        'login', 'authenticate', 'logout', 'forgotPassword', 'resetPassword', 'health'
    ]
    
    private static final List<String> API_PREFIXES = [
        '/api/', '/rest/', '/ws/'
    ]
    
    AuthenticationInterceptor() {
        // Apply to all controllers except excluded ones
        matchAll()
            .excludes(controller: 'login')
            .excludes(controller: 'authentication')
            .excludes(controller: 'public')
            .excludes(controller: 'health')
            .excludes(controller: 'error')
    }
    
    boolean before() {
        // Get client IP address for rate limiting
        String clientIp = getClientIpAddress(request)

        // Apply rate limiting before any other logic, but after exclusion check
        // Exclude static resources and health endpoints from rate limiting as well
        if (!isExcludedForRateLimiting()) {
            if (rateLimitingService.isRateLimited(clientIp)) {
                log.warn("Rate limit exceeded for IP: {}", clientIp)
                renderTooManyRequests("Too many requests from this IP address. Please try again later.")
                // Log rate limiting event
                securityAuditService?.logSecurityEvent([
                    eventType: 'RATE_LIMIT_EXCEEDED',
                    eventDescription: "Rate limit exceeded for IP address",
                    ipAddress: clientIp,
                    userAgent: request.getHeader('User-Agent'),
                    requestUri: request.requestURI,
                    result: 'BLOCKED',
                    errorMessage: "Rate limit exceeded"
                ])
                return false
            }
        }

        // Skip authentication for excluded controllers and actions
        if (isExcluded()) {
            return true
        }
        
        try {
            // Determine if this is an API request
            boolean isApiRequest = isApiRequest()
            
            if (isApiRequest) {
                return handleApiAuthentication()
            } else {
                return handleWebAuthentication()
            }
            
        } catch (Exception e) {
            log.error("Authentication error for ${request.requestURI}", e)
            
            // Log security event
            securityAuditService?.logSecurityEvent([
                eventType: 'AUTHENTICATION_ERROR',
                eventDescription: "Authentication error: ${e.message}",
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                result: 'ERROR',
                errorMessage: e.message
            ])
            
            if (isApiRequest()) {
                renderUnauthorizedJson("Authentication error")
            } else {
                redirect(controller: 'login', action: 'index')
            }
            return false
        }
    }
    
    boolean after() {
        // Clear MDC attributes after request completion
        MDC.clear()
        
        // Log request completion if user is authenticated
        def currentUser = request.getAttribute('currentUser')
        if (currentUser) {
            securityAuditService?.logSecurityEvent([
                eventType: 'REQUEST_COMPLETED',
                eventDescription: "Request completed successfully",
                username: currentUser,
                sessionId: request.getAttribute('currentSessionId'),
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                requestMethod: request.method,
                result: 'SUCCESS',
                processingTime: System.currentTimeMillis() - (request.getAttribute('requestStartTime') ?: System.currentTimeMillis())
            ])
        }
        
        return true
    }
    
    void afterView() {
        // Clean up request attributes
        request.removeAttribute('currentUser')
        request.removeAttribute('currentUserId')
        request.removeAttribute('currentSessionId')
        request.removeAttribute('jwtClaims')
        request.removeAttribute('authenticationMethod')
    }
    
    /**
     * Handles API authentication using JWT.
     * @return True if authentication is successful, false otherwise.
     */
    private boolean handleApiAuthentication() {
        String authHeader = request.getHeader('Authorization')
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            log.warn("API authentication failed: Missing or invalid Authorization header")
            renderUnauthorizedJson("Missing or invalid Authorization header")
            return false
        }

        String token = authHeader.substring(7)
        try {
            Claims claims = jwtTokenService.parseToken(token)
            // Set security context (Spring Security)
            // This part depends on how your Spring Security configuration works
            // For example, if you use a custom authentication token:
            // Authentication authentication = new UsernamePasswordAuthenticationToken(claims.subject, null, /* authorities */)
            // SecurityContextHolder.getContext().setAuthentication(authentication)

            // For direct session-based access control, set session attributes
            // For API requests, session might not be used, relying solely on token.
            // If user_id is needed for audit, retrieve and set here:
            UserMaster user = UserMaster.findByUsername(claims.subject)
            if (user) {
                session.user_id = user.id
                // Set MDC for structured logging
                MDC.put("userId", user.id.toString())
                MDC.put("username", user.username)
                log.debug("MDC set for API request: userId={}, username={}", user.id, user.username)
            } else {
                log.warn("API authentication successful but user {} not found in DB.", claims.subject)
            }

            log.debug("API authentication successful for user: {}", claims.subject)
            securityAuditService?.logSecurityEvent([
                eventType: 'API_AUTHENTICATION_SUCCESS',
                eventDescription: "API login successful",
                username: claims.subject,
                ipAddress: getClientIpAddress(request),
                requestUri: request.requestURI,
                result: 'SUCCESS'
            ])
            return true
        } catch (io.jsonwebtoken.ExpiredJwtException eje) {
            log.warn("API authentication failed: Token expired for {}", eje.claims.subject)
            renderUnauthorizedJson("Authentication token expired")
            securityAuditService?.logSecurityEvent([
                eventType: 'API_AUTHENTICATION_FAILURE',
                eventDescription: "API login failed: Token expired",
                username: eje.claims.subject,
                ipAddress: getClientIpAddress(request),
                requestUri: request.requestURI,
                result: 'FAILED',
                errorMessage: eje.message
            ])
            return false
        } catch (Exception e) {
            log.warn("API authentication failed: Invalid token or other error: {}", e.message)
            renderUnauthorizedJson("Invalid authentication token")
            securityAuditService?.logSecurityEvent([
                eventType: 'API_AUTHENTICATION_FAILURE',
                eventDescription: "API login failed: Invalid token",
                ipAddress: getClientIpAddress(request),
                requestUri: request.requestURI,
                result: 'FAILED',
                errorMessage: e.message
            ])
            return false
        }
    }
    
    /**
     * Handles web (session-based) authentication.
     * @return True if authentication is successful, false otherwise.
     */
    private boolean handleWebAuthentication() {
        // For web authentication, assume Spring Security has already handled login
        // or that user information is already available in the session.
        // If not, redirect to login page.
        if (session.user_id) {
            UserMaster user = UserMaster.get(session.user_id)
            if (user) {
                // Set MDC for structured logging
                MDC.put("userId", user.id.toString())
                MDC.put("username", user.username)
                log.debug("MDC set for web request: userId={}, username={}", user.id, user.username)
            } else {
                log.warn("Web authentication successful but user {} not found in DB for session.user_id.", session.user_id)
            }
            return true
        } else {
            log.debug("Web authentication failed: No user_id in session. Redirecting to login.")
            redirect(controller: 'login', action: 'index')
            return false
        }
    }

    /**
     * Check if current request should be excluded from authentication
     */
    private boolean isExcluded() {
        String controllerName = controllerName?.toLowerCase()
        String actionName = actionName?.toLowerCase()
        
        // Check excluded controllers
        if (controllerName in EXCLUDED_CONTROLLERS) {
            return true
        }
        
        // Check excluded actions
        if (actionName in EXCLUDED_ACTIONS) {
            return true
        }
        
        // Check for health check endpoints
        if (request.requestURI?.contains('/health') || 
            request.requestURI?.contains('/actuator')) {
            return true
        }
        
        // Check for static resources
        if (request.requestURI?.matches(/.*\.(css|js|png|jpg|jpeg|gif|ico|woff|woff2|ttf|svg)$/)) {
            return true
        }
        
        return false
    }

    /**
     * Check if current request should be excluded from rate limiting
     */
    private boolean isExcludedForRateLimiting() {
        // Exclude static resources, health endpoints, and explicitly excluded controllers/actions from rate limiting
        return isExcluded() || 
               request.requestURI?.contains('/health') || 
               request.requestURI?.contains('/actuator')
    }

    /**
     * Get client IP address considering proxies
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader('X-Forwarded-For')
        if (xForwardedFor && !xForwardedFor.isEmpty() && !'unknown'.equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(',')[0].trim()
        }
        
        String xRealIp = request.getHeader('X-Real-IP')
        if (xRealIp && !xRealIp.isEmpty() && !'unknown'.equalsIgnoreCase(xRealIp)) {
            return xRealIp
        }
        
        return request.remoteAddr
    }

    /**
     * Render JSON response for too many requests (HTTP 429)
     */
    private void renderTooManyRequests(String message) {
        response.status = HttpStatus.TOO_MANY_REQUESTS.value()
        response.setContentType("application/json")
        response.writer << "{\"error\": \"${message}\"}"
    }
    
    /**
     * Render JSON response for unauthorized access (HTTP 401)
     */
    private void renderUnauthorizedJson(String message) {
        response.status = HttpStatus.UNAUTHORIZED.value()
        response.setContentType("application/json")
        response.writer << "{\"error\": \"${message}\"}"
    }

    /**
     * Check if this is an API request
     */
    private boolean isApiRequest() {
        String uri = request.requestURI?.toLowerCase()
        String contentType = request.contentType?.toLowerCase()
        String accept = request.getHeader('Accept')?.toLowerCase()
        
        // Check URI patterns
        if (API_PREFIXES.any { uri?.startsWith(it) }) {
            return true
        }
        
        // Check content type
        if (contentType?.contains('application/json') || 
            contentType?.contains('application/xml')) {
            return true
        }
        
        // Check Accept header
        if (accept?.contains('application/json') || 
            accept?.contains('application/xml')) {
            return true
        }
        
        return false
    }
}

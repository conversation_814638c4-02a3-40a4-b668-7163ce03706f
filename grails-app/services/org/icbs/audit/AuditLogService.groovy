package org.icbs.audit

import org.grails.web.util.WebUtils
import javax.servlet.http.HttpSession
import org.springframework.web.context.request.RequestContextHolder
import grails.gorm.transactions.Transactional
import org.springframework.scheduling.annotation.Async
import groovy.util.logging.Slf4j

import org.icbs.lov.AuditType
import org.icbs.admin.Module
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.audit.AuditLogDetail

@Transactional
@Slf4j
class AuditLogService {
    static transactional = true

    @Async
    def insert(String auditCode, String moduleCode, String description, String tableName, newDomainObj, oldDomainObj, String recordUrl, Long recordId) {
    	def webUtils = WebUtils.retrieveGrailsWebRequest()
    	def request = webUtils.getCurrentRequest()
    	def ipAddress = request.getRemoteAddr()
    	HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()

    	def auditLog = new AuditLog()
    	auditLog.auditType = AuditType.findByCode(auditCode)
    	auditLog.module = Module.findByCode(moduleCode)
        auditLog.description = description
        auditLog.tableName = tableName
    	auditLog.date = new Date()
        auditLog.runDate = Branch.get(1).runDate

    	auditLog.userMaster = UserMaster.get(session.user_id)
    	auditLog.ipAddress = ipAddress
    	auditLog.recordUrl = recordUrl
        auditLog.recordId = recordId

        if (auditLog.hasErrors()) {
            log.error("AuditLog has validation errors: {}", auditLog.errors)
            return
        }

    	auditLog.save(flush:true, validateOnError:true)

        // Log Details
        this.logDetails(auditLog, newDomainObj, oldDomainObj)
    }

    /**
     * Logs granular details of changes between two domain objects.
     * Handles creation, update, and deletion scenarios.
     *
     * @param auditLog The main AuditLog entry this detail belongs to.
     * @param newDomainObj The new state of the domain object (can be null for deletion).
     * @param oldDomainObj The old state of the domain object (can be null for creation).
     */
    private void logDetails(AuditLog auditLog, def newDomainObj, def oldDomainObj) {
        if (!auditLog) {
            log.error("AuditLog object is null, cannot log details.")
            return
        }

        if (newDomainObj == null && oldDomainObj == null) {
            log.warn("Both newDomainObj and oldDomainObj are null. No details to log.")
            return
        }

        // Determine which object to iterate over based on operation type
        def targetObject = newDomainObj ?: oldDomainObj
        if (!targetObject) {
            log.error("Target object for logging details is null after checks.")
            return
        }

        // Exclude common Grails internal properties and transient ones
        def excludedProperties = [
            'class', 'metaClass', 'errors', 'hasErrors', 'properties', 'constraints',
            'dateCreated', 'lastUpdated', 'version', 'id'
        ]
        // Add any specific transient properties you don't want to audit
        targetObject.gormPersistentProperties.each { p ->
            if (p.isOneToMany() || p.isManyToMany() || p.isEmbedded() || p.isManyToOne()) {
                // Exclude associations for detailed value comparison to avoid deep recursion or complex logging
                excludedProperties << p.name
            }
        }


        targetObject.properties.each { key, value ->
            if (excludedProperties.contains(key)) {
                return
            }

            def oldVal = null
            def newVal = null

            if (newDomainObj && oldDomainObj) {
                // Update operation
                oldVal = oldDomainObj."$key"
                newVal = newDomainObj."$key"

                if (oldVal != newVal) {
                    new AuditLogDetail(
                        auditLog: auditLog,
                        fieldName: key,
                        oldValue: formatValueForAudit(oldVal),
                        newValue: formatValueForAudit(newVal)
                    ).save(flush: true, failOnError: true)
                }
            } else if (newDomainObj) {
                // Create operation: old value is null, new value is present
                newVal = newDomainObj."$key"
                new AuditLogDetail(
                    auditLog: auditLog,
                    fieldName: key,
                    oldValue: null,
                    newValue: formatValueForAudit(newVal)
                ).save(flush: true, failOnError: true)
            } else if (oldDomainObj) {
                // Delete operation: new value is null, old value is present
                oldVal = oldDomainObj."$key"
                new AuditLogDetail(
                    auditLog: auditLog,
                    fieldName: key,
                    oldValue: formatValueForAudit(oldVal),
                    newValue: null
                ).save(flush: true, failOnError: true)
            }
        }
    }

    /**
     * Formats a given value for audit logging, converting complex objects to a string representation.
     * @param value The value to format.
     * @return A string representation of the value suitable for logging.f
     */
    private String formatValueForAudit(def value) {
        if (value == null) {
            return null
        }
        // Handle Grails domain objects (persistent entities)
        if (value instanceof grails.gorm.GormEntity<?>) {
            return "${value.class.simpleName} (ID: ${value.id})"
        }
        // Handle collections (e.g., hasMany relationships). Log size or a summary.
        if (value instanceof Collection || value.class.isArray()) {
            return "Collection/Array of size ${value.size()}"
        }
        // For other complex objects (e.g., maps, custom classes), try to convert to a meaningful string.
        if (!value.getClass().isPrimitive() &&
            !(value instanceof Number) &&
            !(value instanceof Boolean) &&
            !(value instanceof String) &&
            !(value instanceof Date) &&
            !(value instanceof Enum) &&
            !(value instanceof UUID)
        ) {
            try {
                return value.toString()
            } catch (Exception e) {
                return "Complex Object [${value.class.simpleName}]"
            }
        }
        return value.toString()
    }
}
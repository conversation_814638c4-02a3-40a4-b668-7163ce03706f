package org.icbs.cif

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.cif.Customer
import org.icbs.lov.CustomerStatus
import org.icbs.lov.CustomerType
import org.icbs.common.CommonUtilityService
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import org.icbs.exception.CustomerCreationException
import org.icbs.exception.CustomerUpdateException

/**
 * Customer Business Logic Service
 * Handles comprehensive customer business rules, validation,
 * and lifecycle management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class CustomerBusinessService {
    
    CommonUtilityService commonUtilityService
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def auditLogService
    
    // =====================================================
    // CUSTOMER LIFECYCLE MANAGEMENT
    // =====================================================
    
    /**
     * Create new customer with comprehensive business rule validation
     */
    Customer createCustomer(Map params) {
        return Customer.withTransaction { status ->
            // Map result = [success: false, errors: [], customer: null]
            
            try {
                // 1. Comprehensive validation
                Map validation = validateNewCustomer(params)
                if (!validation.isValid) {
                    // result.errors.addAll(validation.errors)
                    throw new CustomerCreationException("Customer validation failed", validation.errors as List<String>)
                }
                
                // 2. Duplicate detection
                List duplicates = findPotentialDuplicates(params)
                if (duplicates) {
                    // result.warnings = ["Potential duplicate customers found: ${duplicates.size()} matches"]
                    // result.duplicates = duplicates
                    log.warn("Potential duplicate customers found: ${duplicates.size()} matches for params: ${params}")
                    // Allow creation but flag for review (or throw a specific exception if business rules dictate)
                    // For now, proceed with a warning
                }
                
                // 3. Create customer with business rules
                Customer customer = new Customer()
                populateCustomerData(customer, params)
                
                // 4. Apply business rules
                applyCustomerBusinessRules(customer)
                
                // 5. Generate customer ID
                customer.customerId = generateCustomerId(customer)
                
                // 6. Set initial status and type
                customer.status = CustomerStatus.get(1) // Active
                customer.type = CustomerType.get(params.customerType?.id ?: 1)
                
                // 7. Save customer
                if (!customer.save(flush: true)) {
                    // result.errors = extractValidationErrors(customer)
                    throw new CustomerCreationException("Failed to save customer", extractValidationErrors(customer) as List<String>)
                }
                
                // 8. Post-creation processing
                performPostCreationTasks(customer)
                
                // 9. Audit logging
                securityAuditService.logSecurityEvent([
                    eventType: 'CUSTOMER_CREATED',
                    eventDescription: "New customer created successfully",
                    customerId: customer.customerId,
                    customerName: customer.displayName,
                    result: 'SUCCESS'
                ])
                
                // result.success = true
                // result.customer = customer
                // result.message = "Customer created successfully"
                return customer
                
            } catch (CustomerCreationException e) {
                status.setRollbackOnly()
                securityAuditService.logSecurityEvent([
                    eventType: 'CUSTOMER_CREATION_ERROR',
                    eventDescription: "Customer creation failed",
                    result: 'FAILURE',
                    errorMessage: e.message,
                    validationErrors: e.errors
                ])
                throw e
            } catch (Exception e) {
                log.error("Error creating customer", e)
                status.setRollbackOnly()
                // result.errors << "Customer creation failed: ${e.message}"
                
                securityAuditService.logSecurityEvent([
                    eventType: 'CUSTOMER_CREATION_ERROR',
                    eventDescription: "Customer creation failed",
                    result: 'FAILURE',
                    errorMessage: e.message
                ])
                throw new CustomerCreationException("An unexpected error occurred during customer creation: ${e.message}", [e.message])
            }
            
            // return result
        }
    }
    
    /**
     * Update customer with business rule validation
     */
    Customer updateCustomer(Long customerId, Map params) {
        return Customer.withTransaction { status ->
            // Map result = [success: false, errors: [], customer: null]
            
            try {
                Customer customer = Customer.get(customerId)
                if (!customer) {
                    // result.errors << "Customer not found"
                    throw new CustomerUpdateException("Customer not found for ID: ${customerId}")
                }
                
                // Store original values for audit
                Map originalValues = captureCustomerSnapshot(customer)
                
                // 1. Validate updates
                Map validation = validateCustomerUpdate(customer, params)
                if (!validation.isValid) {
                    // result.errors.addAll(validation.errors)
                    throw new CustomerUpdateException("Customer update validation failed", validation.errors as List<String>)
                }
                
                // 2. Apply updates
                updateCustomerData(customer, params)
                
                // 3. Re-apply business rules
                applyCustomerBusinessRules(customer)
                
                // 4. Save changes
                if (!customer.save(flush: true)) {
                    // result.errors = extractValidationErrors(customer)
                    throw new CustomerUpdateException("Failed to save customer updates", extractValidationErrors(customer) as List<String>)
                }
                
                // 5. Audit changes
                auditCustomerChanges(customer, originalValues, params)
                
                // result.success = true
                // result.customer = customer
                // result.message = "Customer updated successfully"
                return customer
                
            } catch (CustomerUpdateException e) {
                status.setRollbackOnly()
                securityAuditService.logSecurityEvent([
                    eventType: 'CUSTOMER_UPDATE_ERROR',
                    eventDescription: "Customer update failed",
                    customerId: customerId,
                    result: 'FAILURE',
                    errorMessage: e.message,
                    validationErrors: e.errors
                ])
                throw e
            } catch (Exception e) {
                log.error("Error updating customer", e)
                status.setRollbackOnly()
                // result.errors << "Customer update failed: ${e.message}"
                throw new CustomerUpdateException("An unexpected error occurred during customer update: ${e.message}", [e.message])
            }
            
            // return result
        }
    }
    
    /**
     * Comprehensive customer validation for new customers
     */
    private Map validateNewCustomer(Map params) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        // 1. Basic field validation
        Map basicValidation = unifiedValidationService.validateCustomer(params)
        if (!basicValidation.isValid) {
            result.isValid = false
            result.errors.addAll(basicValidation.errors)
        }
        result.warnings.addAll(basicValidation.warnings)
        
        // 2. Business rule validation
        
        // Age validation
        if (params.birthDate) {
            Date birthDate = params.birthDate instanceof Date ? 
                params.birthDate : Date.parse('yyyy-MM-dd', params.birthDate.toString())
            
            int age = calculateAge(birthDate)
            if (age < 18) {
                result.isValid = false
                result.errors << "Customer must be at least 18 years old"
            } else if (age > 120) {
                result.isValid = false
                result.errors << "Invalid birth date - age cannot exceed 120 years"
            }
        }
        
        // Name validation
        if (params.name1) {
            if (params.name1.toString().length() < 2) {
                result.isValid = false
                result.errors << "First name must be at least 2 characters"
            }
            if (params.name1.toString().matches(/.*\d.*/)) {
                result.warnings << "First name contains numbers"
            }
        }
        
        // Phone number validation
        if (params.phone) {
            if (!unifiedValidationService.isValidPhone(params.phone.toString())) {
                result.warnings << "Phone number format may be invalid"
            }
        }
        
        // Email validation
        if (params.email) {
            if (!unifiedValidationService.isValidEmail(params.email.toString())) {
                result.isValid = false
                result.errors << "Invalid email format"
            }
        }
        
        // Address validation
        if (!params.address1 || params.address1.toString().trim().isEmpty()) {
            result.isValid = false
            result.errors << "Primary address is required"
        }
        
        return result
    }
    
    /**
     * Find potential duplicate customers
     */
    private List findPotentialDuplicates(Map params) {
        try {
            return commonUtilityService.findDuplicateCustomers(params)
        } catch (Exception e) {
            log.error("Error finding duplicate customers", e)
            return []
        }
    }
    
    /**
     * Populate customer data from parameters
     */
    private void populateCustomerData(Customer customer, Map params) {
        // Personal information
        customer.name1 = params.name1?.toString()?.trim()
        customer.name2 = params.name2?.toString()?.trim()
        customer.name3 = params.name3?.toString()?.trim()
        customer.name4 = params.name4?.toString()?.trim()
        customer.lastName = params.lastName?.toString()?.trim()
        customer.firstName = params.firstName?.toString()?.trim()
        
        // Generate display name
        customer.displayName = generateDisplayName(customer)
        
        // Contact information
        customer.email = params.email?.toString()?.trim()?.toLowerCase()
        customer.phone = params.phone?.toString()?.trim()
        customer.mobile = params.mobile?.toString()?.trim()
        
        // Address information
        customer.address1 = params.address1?.toString()?.trim()
        customer.address2 = params.address2?.toString()?.trim()
        customer.city = params.city?.toString()?.trim()
        customer.state = params.state?.toString()?.trim()
        customer.zipCode = params.zipCode?.toString()?.trim()
        customer.country = params.country?.toString()?.trim()
        
        // Personal details
        if (params.birthDate) {
            customer.birthDate = params.birthDate instanceof Date ? 
                params.birthDate : Date.parse('yyyy-MM-dd', params.birthDate.toString())
        }
        
        // Financial information
        if (params.annualIncome) {
            customer.annualIncome = new BigDecimal(params.annualIncome.toString())
        }
        
        // Set creation metadata
        customer.dateCreated = new Date()
        customer.lastUpdated = new Date()
    }
    
    /**
     * Apply customer business rules
     */
    private void applyCustomerBusinessRules(Customer customer) {
        // 1. Set customer risk profile based on income and age
        setCustomerRiskProfile(customer)
        
        // 2. Determine customer tier based on profile
        setCustomerTier(customer)
        
        // 3. Set default transaction limits
        setDefaultTransactionLimits(customer)
        
        // 4. Apply regulatory requirements
        applyRegulatoryRequirements(customer)
    }
    
    /**
     * Generate unique customer ID
     */
    private String generateCustomerId(Customer customer) {
        String prefix = "CUS"
        String timestamp = System.currentTimeMillis().toString().substring(8)
        String random = String.format("%03d", (Math.random() * 1000).toInteger())
        
        String customerId = "${prefix}${timestamp}${random}"
        
        // Ensure uniqueness
        while (Customer.findByCustomerId(customerId)) {
            random = String.format("%03d", (Math.random() * 1000).toInteger())
            customerId = "${prefix}${timestamp}${random}"
        }
        
        return customerId
    }
    
    /**
     * Generate display name from customer names
     */
    private String generateDisplayName(Customer customer) {
        StringBuilder displayName = new StringBuilder()
        
        if (customer.firstName) {
            displayName.append(customer.firstName)
        }
        if (customer.lastName) {
            if (displayName.length() > 0) displayName.append(" ")
            displayName.append(customer.lastName)
        }
        if (displayName.length() == 0 && customer.name1) {
            displayName.append(customer.name1)
            if (customer.name2) {
                displayName.append(" ").append(customer.name2)
            }
        }
        
        return displayName.toString().trim()
    }
    
    /**
     * Calculate age from birth date
     */
    private int calculateAge(Date birthDate) {
        Calendar birth = Calendar.getInstance()
        birth.setTime(birthDate)
        
        Calendar now = Calendar.getInstance()
        
        int age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR)
        
        if (now.get(Calendar.DAY_OF_YEAR) < birth.get(Calendar.DAY_OF_YEAR)) {
            age--
        }
        
        return age
    }
    
    /**
     * Set customer risk profile
     */
    private void setCustomerRiskProfile(Customer customer) {
        // Simple risk profiling logic - can be enhanced
        String riskProfile = "MEDIUM"
        
        if (customer.annualIncome) {
            if (customer.annualIncome > 100000) {
                riskProfile = "LOW"
            } else if (customer.annualIncome < 25000) {
                riskProfile = "HIGH"
            }
        }
        
        // Age factor
        if (customer.birthDate) {
            int age = calculateAge(customer.birthDate)
            if (age < 25 || age > 65) {
                if (riskProfile == "LOW") riskProfile = "MEDIUM"
                else if (riskProfile == "MEDIUM") riskProfile = "HIGH"
            }
        }
        
        customer.riskProfile = riskProfile
    }
    
    /**
     * Set customer tier
     */
    private void setCustomerTier(Customer customer) {
        // Default tier logic
        String tier = "STANDARD"
        
        if (customer.annualIncome) {
            if (customer.annualIncome > 250000) {
                tier = "PREMIUM"
            } else if (customer.annualIncome > 100000) {
                tier = "GOLD"
            } else if (customer.annualIncome > 50000) {
                tier = "SILVER"
            }
        }
        
        customer.customerTier = tier
    }
    
    /**
     * Set default transaction limits
     */
    private void setDefaultTransactionLimits(Customer customer) {
        // Set limits based on customer tier
        switch (customer.customerTier) {
            case "PREMIUM":
                customer.dailyTransactionLimit = 100000
                customer.monthlyTransactionLimit = 2000000
                break
            case "GOLD":
                customer.dailyTransactionLimit = 50000
                customer.monthlyTransactionLimit = 1000000
                break
            case "SILVER":
                customer.dailyTransactionLimit = 25000
                customer.monthlyTransactionLimit = 500000
                break
            default:
                customer.dailyTransactionLimit = 10000
                customer.monthlyTransactionLimit = 200000
        }
    }
    
    /**
     * Apply regulatory requirements
     */
    private void applyRegulatoryRequirements(Customer customer) {
        // KYC requirements
        customer.kycRequired = true
        customer.kycStatus = "PENDING"
        
        // AML screening required for high-risk customers
        if (customer.riskProfile == "HIGH") {
            customer.amlScreeningRequired = true
            customer.amlStatus = "PENDING"
        }
    }
    
    /**
     * Perform post-creation tasks
     */
    private void performPostCreationTasks(Customer customer) {
        try {
            // 1. Generate welcome letter
            generateWelcomeLetter(customer)
            
            // 2. Schedule KYC verification
            scheduleKYCVerification(customer)
            
            // 3. Create default accounts if needed
            createDefaultAccounts(customer)
            
        } catch (Exception e) {
            log.error("Error in post-creation tasks for customer ${customer.customerId}", e)
        }
    }
    
    // =====================================================
    // HELPER METHODS
    // =====================================================
    
    private void generateWelcomeLetter(Customer customer) {
        // Implementation for welcome letter generation
        log.info("Welcome letter generated for customer: ${customer.customerId}")
    }
    
    private void scheduleKYCVerification(Customer customer) {
        // Implementation for KYC scheduling
        log.info("KYC verification scheduled for customer: ${customer.customerId}")
    }
    
    private void createDefaultAccounts(Customer customer) {
        // Implementation for default account creation
        log.info("Default accounts created for customer: ${customer.customerId}")
    }
    
    private Map captureCustomerSnapshot(Customer customer) {
        return [
            name1: customer.name1,
            name2: customer.name2,
            email: customer.email,
            phone: customer.phone,
            address1: customer.address1
            // Add other fields as needed
        ]
    }
    
    private Map validateCustomerUpdate(Customer customer, Map params) {
        // Implementation for update validation
        return [isValid: true, errors: []]
    }

    private void updateCustomerData(Customer customer, Map params) {
        // Implementation for updating customer data
        populateCustomerData(customer, params)
    }

    private void auditCustomerChanges(Customer customer, Map originalValues, Map params) {
        // Implementation for change auditing
        auditLogService?.logCustomerChange(customer, originalValues, params)
    }

    private List extractValidationErrors(def domainObject) {
        List errors = []
        domainObject.errors.allErrors.each { error ->
            errors << message(error: error)
        }
        return errors
    }

    private String message(error) {
        // This is a placeholder for getting localized messages
        // In a real Grails app, you'd use messageSource or g.message taglib
        "Error on field '${error.field}': ${error.defaultMessage}"
    }

    private void auditBulkDocumentUpload(Customer customer, Integer uploadedCount, String result) {
        try {
            auditLogService.insert('120', 'CDC05500', 
                "Bulk customer documents uploaded - Customer: ${customer.id}, Count: ${uploadedCount}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing bulk document upload", e)
        }
    }
}

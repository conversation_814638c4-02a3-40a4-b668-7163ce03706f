package org.icbs.cif

import grails.gorm.transactions.Transactional
import grails.util.Holders
import grails.converters.JSON

import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.CustomerStatus
import org.icbs.lov.CustomerType
import org.icbs.lov.Gender
import org.icbs.lov.Lov
import org.icbs.admin.UserMaster
import org.icbs.deposit.Deposit
import groovy.util.logging.Slf4j
import org.icbs.cif.CustomerRegistrationCommand
import org.icbs.cif.ContactCommand
import org.icbs.cif.AddressCommand
import org.icbs.admin.Branch
import org.icbs.common.SanitizationService
import org.springframework.cache.annotation.Cacheable
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Caching
import org.springframework.scheduling.annotation.Async

@Slf4j
@Transactional
class CustomerService {
    def auditLogService
    def policyService
    def sanitizationService
    boolean transactional = false    

    // Reusable helper for handling transaction rollback and error reporting
    private def handleTransactionFailure(def status, Map result, Map errorDetails) {
        status.setRollbackOnly()
        result.success = false // Indicate failure
        if (result.customerInstance?.errors) {
            result.errors = result.customerInstance.errors
        } else if (errorDetails.code) {
            // Simplified error message generation for now, consider more detailed if needed
            result.errors = [new grails.validation.ValidationErrors(new Customer()).rejectValue(errorDetails.field, errorDetails.code)]
        }
        return result
    }

    @Caching(evict = [
        @CacheEvict(value = "customers", key = "#params.id"),
        @CacheEvict(value = "customerList", allEntries = true)
    ])
    def update(params,includeList=null) {
        log.debug("includelist: ${includeList}")
        log.debug("params: ${params}")
        Customer.withTransaction { status ->
            def result = [:]
            result.customerInstance = getCustomerById(params.id as Long)
            if(!result.customerInstance){
                return handleTransactionFailure(status, result, [code:"default.not.found"])
            }
            // Optimistic locking check.
            if(params.version) {
                if(result.customerInstance.version > params.version.toLong())
                    return handleTransactionFailure(status, result, [field:"version", code:"default.optimistic.locking.failure"])
            }
            if(!includeList){
                result.customerInstance.properties = params
            }
            else{
                result.customerInstance.properties[includeList]=params
            }
             log.debug("Passed validation!")
            /*Help process one to many functions relationships*/
            result.customerInstance = deleteHelper(result.customerInstance)
            if(!includeList){
                if(!result.customerInstance.validate()){    
                    return handleTransactionFailure(status, result, [code:"default.update.failure"])
                }
            }else{
                if(!result.customerInstance.validate([includeList])){    
                    return handleTransactionFailure(status, result, [code:"default.update.failure"])
                }
            }
            result.customerInstance.save()  
            // update Account Names when customer was editted
            updateCustomerAccountNames(params)
            // Log
            //def description = 'save customer ' +  result.customerInstance.customerId
            //auditLogService.insert('060', 'CIF00000', description, 'customer', null, null, null)
            
            // Success.
            return result
        } //end withTransaction
    }  // end update()

    @Async // Mark the method for asynchronous execution
    def updateCustomerAccountNames(params) {
        //updateCustomerAccountNames
        def customerInstance = getCustomerById(params.id as Long)

        // Optimized: Use HQL for bulk update instead of N+1 selects and updates
        String newAcctName = "${customerInstance.name1} ${customerInstance.name2 ?: ''} ${customerInstance.name3 ?: ''}".trim()
        int updatedCount = Deposit.executeUpdate("update Deposit d set d.acctName = :newAcctName where d.customer = :customer",
            [newAcctName: newAcctName, customer: customerInstance])

        log.debug("Updated account names for ${updatedCount} deposit instances for customer ${customerInstance.id}")
        
    }

    @Caching(evict = [
        @CacheEvict(value = "customers", key = "#result.customer.id", condition = "#result.success"),
        @CacheEvict(value = "customerList", allEntries = true, condition = "#result.success")
    ])
    def save(CustomerRegistrationCommand cmd) {
        log.info("Attempting to save new customer from command object: ${cmd.name1}")
        Customer.withTransaction { status ->
            def result = [success: false, customer: null, errors: null] // Initialize result map

            def customerInstance = new Customer()

            // Map properties from command to domain instance and sanitize relevant fields
            customerInstance.type = cmd.type
            customerInstance.branch = cmd.branch
            customerInstance.name1 = sanitizationService.sanitizeForHtml(cmd.name1)
            customerInstance.name2 = sanitizationService.sanitizeForHtml(cmd.name2)
            customerInstance.name3 = sanitizationService.sanitizeForHtml(cmd.name3)
            customerInstance.name4 = sanitizationService.sanitizeForHtml(cmd.name4)
            customerInstance.name5 = cmd.name5
            customerInstance.displayName = sanitizationService.sanitizeForHtml(cmd.displayName)
            customerInstance.shortAddress = sanitizationService.sanitizeForHtml(cmd.shortAddress)
            customerInstance.pepDescription = sanitizationService.sanitizeForHtml(cmd.pepDescription)
            customerInstance.amla = sanitizationService.sanitizeForHtml(cmd.amla)
            customerInstance.birthDate = cmd.birthDate
            customerInstance.title = cmd.title
            customerInstance.gender = cmd.gender
            customerInstance.civilStatus = cmd.civilStatus
            customerInstance.birthPlace = sanitizationService.sanitizeForHtml(cmd.birthPlace)
            customerInstance.isTaxable = cmd.isTaxable
            customerInstance.creditLimit = cmd.creditLimit
            customerInstance.customerCode1 = cmd.customerCode1
            customerInstance.customerCode2 = cmd.customerCode2
            customerInstance.customerCode3 = cmd.customerCode3
            customerInstance.nationality = cmd.nationality
            customerInstance.sourceOfIncome = sanitizationService.sanitizeForHtml(cmd.sourceOfIncome)
            customerInstance.dosriCode = cmd.dosriCode
            customerInstance.sssNo = sanitizationService.sanitizeForHtml(cmd.sssNo)
            customerInstance.gisNo = sanitizationService.sanitizeForHtml(cmd.gisNo)
            customerInstance.tinNo = sanitizationService.sanitizeForHtml(cmd.tinNo)
            customerInstance.passportNo = sanitizationService.sanitizeForHtml(cmd.passportNo)
            customerInstance.remarks = sanitizationService.sanitizeForHtml(cmd.remarks)
            customerInstance.group = cmd.group
            customerInstance.status = cmd.status
            customerInstance.noOfDependent = cmd.noOfDependent
            customerInstance.motherMaidenName = sanitizationService.sanitizeForHtml(cmd.motherMaidenName)
            customerInstance.fatherName = sanitizationService.sanitizeForHtml(cmd.fatherName)
            customerInstance.religion = cmd.religion
            customerInstance.spouseLastName = sanitizationService.sanitizeForHtml(cmd.spouseLastName)
            customerInstance.spouseFirstName = sanitizationService.sanitizeForHtml(cmd.spouseFirstName)
            customerInstance.spouseMiddleName = sanitizationService.sanitizeForHtml(cmd.spouseMiddleName)
            customerInstance.spouseBirthDate = cmd.spouseBirthDate
            customerInstance.spouseContactNo = sanitizationService.sanitizeForHtml(cmd.spouseContactNo)
            customerInstance.membership = cmd.membership
            customerInstance.custBirthPlace = cmd.custBirthPlace

            // Assuming createdByUserId is available in the command for audit purposes
            // This would typically come from the authenticated user's session
            // For now, I'm using a placeholder; you'll need to replace cmd.createdByUserId
            // with the actual way you get the current user's ID.
            def currentUser = UserMaster.get(1) // Placeholder: Replace with actual current user lookup
            if (currentUser) {
                customerInstance.createdBy = currentUser
                customerInstance.lastUpdatedBy = currentUser
            } else {
                log.warn("Current user not found for customer creation. Audit trail may be incomplete.")
                // Handle this case appropriately, e.g., throw an error or set a default user
            }
            customerInstance.createdAt = new Date()
            customerInstance.lastUpdatedAt = new Date()

            // Handle contacts and sanitize
            cmd.contacts?.each { contactCmd ->
                def contact = new Contact(
                    isPrimaryPhone: contactCmd.isPrimaryPhone,
                    isPrimaryEmail: contactCmd.isPrimaryEmail,
                    type: contactCmd.type,
                    contactValue: sanitizationService.sanitizeForHtml(contactCmd.contactValue),
                    status: contactCmd.status
                )
                customerInstance.addToContacts(contact)
            }

            // Handle addresses and sanitize
            cmd.addresses?.each { addressCmd ->
                def address = new Address(
                    type: addressCmd.type,
                    isPrimary: addressCmd.isPrimary,
                    isMailing: addressCmd.isMailing,
                    isOwned: addressCmd.isOwned,
                    isMortaged: addressCmd.isMortaged,
                    isRented: addressCmd.isRented,
                    address1: sanitizationService.sanitizeForHtml(addressCmd.address1),
                    address2: sanitizationService.sanitizeForHtml(addressCmd.address2),
                    address3: sanitizationService.sanitizeForHtml(addressCmd.address3),
                    postalCode: sanitizationService.sanitizeForHtml(addressCmd.postalCode),
                    residenceSince: sanitizationService.sanitizeForHtml(addressCmd.residenceSince),
                    countryId: addressCmd.countryId,
                    town: addressCmd.town,
                    phone1: sanitizationService.sanitizeForHtml(addressCmd.phone1),
                    phone2: sanitizationService.sanitizeForHtml(addressCmd.phone2),
                    phone3: sanitizationService.sanitizeForHtml(addressCmd.phone3),
                    phone4: sanitizationService.sanitizeForHtml(addressCmd.phone4),
                    addressSince: addressCmd.addressSince,
                    shortAddress: sanitizationService.sanitizeForHtml(addressCmd.shortAddress),
                    longLatLocation: sanitizationService.sanitizeForHtml(addressCmd.longLatLocation),
                    addressContact: sanitizationService.sanitizeForHtml(addressCmd.addressContact),
                    remarks: sanitizationService.sanitizeForHtml(addressCmd.remarks),
                    status: addressCmd.status,
                    languageId: addressCmd.languageId
                )
                customerInstance.addToAddresses(address)
            }

            // Save the customer instance
            if (!customerInstance.save(flush: true)) {
                log.error("Failed to save customer instance during initial save: ${customerInstance.errors}")
                result.customerInstance = customerInstance // Attach for error mapping
                return handleTransactionFailure(status, result, [code: "default.save.failure"])
            }

            // Generate customer ID and save again
            customerInstance = customerIdBuilder(customerInstance)
            if (!customerInstance.save(flush: true)) {
                log.error("Failed to save customer instance after ID generation: ${customerInstance.errors}")
                result.customerInstance = customerInstance // Attach for error mapping
                return handleTransactionFailure(status, result, [code: "default.save.failure"])
            }

            result.success = true
            result.customer = customerInstance
            return result
        }
    }
    @Cacheable("customers")
    Customer getCustomerById(Long id) {
        log.debug("Fetching customer with ID: ${id} from database (not cache)")
        return Customer.get(id)
    }
    private customerIdBuilder(customerInstance){
        String branchCode =String.format("%03d", customerInstance.branch.code)
        String serial = String.format("%07d", customerInstance.id);
        branchCode= branchCode.substring(0, Math.min(branchCode.length(), 3));
        customerInstance.customerId = branchCode+"-"+serial
        return customerInstance
    }
    /*Takes care of deleting one to many relations that are deleted*/
    @CacheEvict(value = "customers", key = "#customerInstance.id")
    public deleteHelper(customerInstance){
        
        /*Deleting nulls in one to many relationships*/
        if(customerInstance.contacts){customerInstance.contacts.removeAll([null])}
        if(customerInstance.addresses){customerInstance.addresses.removeAll([null])}
        if(customerInstance.educations){customerInstance.educations.removeAll([null])}
        if(customerInstance.relations){customerInstance.relations.removeAll([null])}
        if(customerInstance.attachments){customerInstance.attachments.removeAll([null])}
        if(customerInstance.presentedids){customerInstance.presentedids.removeAll([null])}
        if(customerInstance.otheraccts){customerInstance.otheraccts.removeAll([null])}
        if(customerInstance.relations){customerInstance.relations.removeAll([null])}
       
        /*Deleted One to many Relationships are proccessed here. Soft Delete*/
        // Helper to process soft deletions for a collection
        def processSoftDelete = { collection, entityName ->
            if (collection) {
                def itemsToDelete = collection.findAll { it.deleted }
                if (itemsToDelete) {
                    log.debug("${entityName} to be Deleted: ${itemsToDelete.size()} items")
                    itemsToDelete.each { item ->
                        item?.status = ConfigItemStatus.get(3)
                    }
                }
            }
        }

        processSoftDelete(customerInstance.relations, "Relations")
        processSoftDelete(customerInstance.contacts, "Contacts")
        processSoftDelete(customerInstance.addresses, "Addresses")
        processSoftDelete(customerInstance.educations, "Education")
        processSoftDelete(customerInstance.attachments, "Attachment")
        processSoftDelete(customerInstance.otheraccts, "OtherAccts")
        processSoftDelete(customerInstance.presentedids, "PresentedIds")

        // Log      
        return customerInstance
    }
    /*Childlist is an array of an array*/
    // Removed preparePostResponse method - will use Grails' built-in render/respond with errors
    // def preparePostResponse(domainInstance,matches=null) {
    //     def grailsApplication = Holders.getGrailsApplication()
    //     def g = grailsApplication.mainContext.getBean( 'org.codehaus.groovy.grails.plugins.web.taglib.ApplicationTagLib' )
        
    //     def postResponse = new AjaxPostResponse()
    //     postResponse.domainObject = domainInstance
    //     if(domainInstance.hasErrors()){
    //         g.eachError(bean: domainInstance) {
    //             if(!matches){
    //                 postResponse.errors."${it.field}" = g.message(error: it)
    //             }else{
    //                 if(matches instanceof List <?>){
    //                     for(i in 0..matches.size()-1){
    //                         if((it.field).matches(matches.get(i))){
    //                             postResponse.errors."${it.field}" = g.message(error: it)
    //                         }
    //                     }
    //                 }else{
    //                     if((it.field).matches(matches)){
    //                             postResponse.errors."${it.field}" = g.message(error: it)
    //                         }
    //                 }  
    //             }
    //             log.debug(it)
    //         }
    //     }
    //     log.debug(postResponse.errors)
    //     if(postResponse.errors){ 
    //         postResponse.success = false
    //         postResponse.message = "There was an error"
    //     } else {
    //         postResponse.success = true
    //         postResponse.message = "Success"
    //     } 
    //     return postResponse 
    // }
    // Removed validate method - validation is handled by UnifiedValidationService or directly in service methods
    // def validate(domainInstance,childList=null,maxDepth=null){
    //     def a = new customErrorValidation(domainInstance,childList,maxDepth)
    //     a.validateInstance()
    //     return a.getPostResponse()
    // }
}

// Removed AjaxPostResponse class - no longer needed with direct render/respond
// class AjaxPostResponse {
//     boolean success
//     String message
//     String html
//     def domainObject
//     def errors = [:] 
// }
// class customErrorValidation{
//     int maxDepth
//     AjaxPostResponse postResponse
//     def childList
//     def grailsApplication
//     def g
//     def customErrorValidation(domainInstance,childList=null,maxDepth=null){
//         this.grailsApplication = Holders.getGrailsApplication()
//         this.g = grailsApplication.mainContext.getBean( 'org.codehaus.groovy.grails.plugins.web.taglib.ApplicationTagLib' )
//         this.postResponse = new AjaxPostResponse()
//         if(childList)this.childList = childList
//         this.postResponse.domainObject = domainInstance
//         if(maxDepth)this.maxDepth = maxDepth
//     }
//     def validateInstance(){
//         if(this.childList==null){
//             if(this.postResponse.domainObject.hasErrors()){
//                 g.eachError(bean: postResponse.domainObject) {
//                     this.postResponse.errors."${it.field}" = g.message(error: it)
//                 }
//             }
//         }else if(childList){ 
//             recursiveValidate(childList,0,0)
//         }
//         if(this.postResponse.errors){ 
//             this.postResponse.success = false
//             this.postResponse.message = "There was an error"
//         } else {
//             this.postResponse.success = true
//             this.postResponse.message = "Success"
//         }
//         return
//     }
//     private recursiveValidate(subsetList,childNumber,curDepth){
//         def tempSubsetList = subsetList
//         if(!subsetList){
//             return
//         }
//         if(this.maxDepth){
//             if(curDepth>this.maxDepth){
//                 return 
//             }
//         }
//          if(subsetList instanceof List <?>){
//             log.debug("List Is an Array List *******" + curDepth)
//             for(i in 0 .. subsetList.size()-1) {
//                 def object = subsetList.get(i)
//                 log.debug"child"+ i+" class="+ object.getClass()
//                 recursiveValidate(object,i,++curDepth)
//                 log.debug("Total Error List"+this.postResponse.errors)
//             }
//         }else {
//             tempSubsetList.validate()
//             g.eachError(bean: subsetList) {
//                 log.debug " terminal child" +subsetList.getClass()
//                 this.postResponse.errors."${it.field+childNumber}" = g.message(error: it)
//                 log.debug"post response errors on terminal" +this.postResponse.errors
//             }
//         } 
//         return 
//     }   
//     def getPostResponse(){
//         return this.postResponse
//     }
// }

package org.icbs.common

import groovy.transform.CompileStatic
import org.owasp.encoder.Encode

@CompileStatic
class SanitizationService {

    /**
     * Encodes a string for safe HTML output, preventing XSS attacks.
     * Uses OWASP ESAPI Encoder.Encode.forHtml(input) to handle common HTML contexts.
     * @param input The string to be sanitized.
     * @return The sanitized string.
     */
    String sanitizeForHtml(String input) {
        if (input == null) {
            return null
        }
        // Example using OWASP ESAPI Encoder. It's crucial to use a robust library.
        // If ESAPI is not available, a simpler (less comprehensive) alternative could be
        // org.springframework.web.util.HtmlUtils.htmlEscape(input)
        // or Apache Commons Text StringEscapeUtils.escapeHtml4(input)
        // For production, a dedicated security library like OWASP ESAPI is highly recommended.
        return Encode.forHtml(input)
    }

    /**
     * Encodes a string for safe HTML attribute output.
     * @param input The string to be sanitized.
     * @return The sanitized string.
     */
    String sanitizeForHtmlAttribute(String input) {
        if (input == null) {
            return null
        }
        return Encode.forHtmlAttribute(input)
    }

    /**
     * Encodes a string for safe JavaScript string output.
     * @param input The string to be sanitized.
     * @return The sanitized string.
     */
    String sanitizeForJavaScript(String input) {
        if (input == null) {
            return null
        }
        return Encode.forJavaScript(input)
    }

    /**
     * Strips potentially malicious HTML tags and attributes from a string.
     * This is useful for user-generated content where some HTML formatting is allowed
     * but scripts and dangerous tags should be removed.
     * Requires a HTML sanitization library (e.g., OWASP AntiSamy, Jsoup with Whitelist).
     * For simplicity, this example uses a basic regex, but a proper library is critical.
     * @param input The string to be sanitized.
     * @return The sanitized string.
     */
    String stripHtmlTags(String input) {
        if (input == null) {
            return null
        }
        // WARNING: This is a simplistic example and should be replaced by a robust HTML sanitization library
        // like OWASP AntiSamy or Jsoup with a configured Whitelist for production use.
        return input.replaceAll('<script[^>]*?>[\\s\\S]*?<\\/script>', '')
                    .replaceAll('<style[^>]*?>[\\s\\S]*?<\\/style>', '')
                    .replaceAll('<[^>]+>', '')
    }

    /**
     * Sanitizes a string for safe use in SQL queries to prevent SQL injection.
     * Note: Parameterized queries are the *primary* defense against SQL injection.
     * This method provides a secondary layer for cases where direct string concatenation is unavoidable (rare).
     * @param input The string to be sanitized.
     * @return The sanitized string.
     */
    String sanitizeForSql(String input) {
        if (input == null) {
            return null
        }
        // IMPORTANT: Always use parameterized queries (e.g., GORM query methods, Hibernate Criteria) 
        // as your primary defense against SQL injection. This method is a LAST RESORT.
        // This example is for demonstration; a real implementation would be more complex.
        return input.replace("'", "''").replace("--", "")
    }
} 
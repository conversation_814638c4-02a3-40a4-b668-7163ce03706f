package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.cif.Customer
import org.icbs.loans.Loan
import org.icbs.loans.LoanApplication
import org.icbs.loans.LoanType
import org.icbs.lov.ConfigItemStatus
import org.icbs.common.CommonUtilityService
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import org.icbs.workflow.WorkflowManagementService

/**
 * REFACTORED: Loan Application Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles loan application processing with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanApplicationService {
    
    CommonUtilityService commonUtilityService
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    WorkflowManagementService workflowManagementService
    LoanCalculationService loanCalculationService
    LoanValidationService loanValidationService
    
    // =====================================================
    // LOAN APPLICATION PROCESSING
    // =====================================================
    
    /**
     * Submit new loan application
     */
    Map submitLoanApplication(Map applicationData) {
        return LoanApplication.withTransaction { status ->
            Map result = [success: false, errors: [], application: null]
            
            try {
                // 1. Comprehensive validation
                Map validation = validateLoanApplication(applicationData)
                if (!validation.isValid) {
                    result.errors.addAll(validation.errors)
                    return result
                }
                
                // 2. Check customer eligibility
                Map eligibility = checkCustomerEligibility(applicationData.customerId)
                if (!eligibility.eligible) {
                    result.errors.addAll(eligibility.reasons)
                    return result
                }
                
                // 3. Create loan application
                LoanApplication application = createLoanApplication(applicationData)
                
                // 4. Perform initial credit assessment
                Map creditAssessment = performInitialCreditAssessment(application)
                updateApplicationWithCreditInfo(application, creditAssessment)
                
                // 5. Calculate preliminary loan terms
                Map loanTerms = loanCalculationService.calculatePreliminaryTerms(application)
                updateApplicationWithTerms(application, loanTerms)
                
                // 6. Set initial status and workflow
                application.status = ConfigItemStatus.get(1) // Submitted
                application.applicationNumber = generateApplicationNumber()
                
                // 7. Save application
                if (!application.save(flush: true)) {
                    result.errors = extractValidationErrors(application)
                    return result
                }
                
                // 8. Initialize workflow
                Map workflowResult = workflowManagementService.processStateTransition(
                    'LOAN_APPLICATION',
                    application.id,
                    'DRAFT',
                    'SUBMITTED',
                    [hasRequiredDocuments: true]
                )
                
                // 9. Generate required documents checklist
                generateDocumentChecklist(application)
                
                // 10. Audit logging
                auditLoanApplication('APPLICATION_SUBMITTED', application, 'SUCCESS')
                
                result.success = true
                result.application = application
                result.message = "Loan application submitted successfully"
                result.nextSteps = getNextSteps(application)
                
            } catch (Exception e) {
                log.error("Error submitting loan application", e)
                status.setRollbackOnly()
                result.errors << "Application submission failed: ${e.message}"
                
                auditLoanApplication('APPLICATION_SUBMISSION_ERROR', null, 'FAILURE', e.message)
            }
            
            return result
        }
    }
    
    /**
     * Update existing loan application
     */
    Map updateLoanApplication(Long applicationId, Map updateData) {
        return LoanApplication.withTransaction { status ->
            Map result = [success: false, errors: [], application: null]
            
            try {
                LoanApplication application = LoanApplication.get(applicationId)
                if (!application) {
                    result.errors << "Loan application not found"
                    return result
                }
                
                // 1. Check if application can be updated
                if (!canUpdateApplication(application)) {
                    result.errors << "Application cannot be updated in current status"
                    return result
                }
                
                // 2. Store original values for audit
                Map originalValues = captureApplicationSnapshot(application)
                
                // 3. Validate updates
                Map validation = validateApplicationUpdate(application, updateData)
                if (!validation.isValid) {
                    result.errors.addAll(validation.errors)
                    return result
                }
                
                // 4. Apply updates
                updateApplicationData(application, updateData)
                
                // 5. Recalculate terms if financial data changed
                if (hasFinancialDataChanged(originalValues, updateData)) {
                    Map creditAssessment = performInitialCreditAssessment(application)
                    updateApplicationWithCreditInfo(application, creditAssessment)
                    
                    Map loanTerms = loanCalculationService.calculatePreliminaryTerms(application)
                    updateApplicationWithTerms(application, loanTerms)
                }
                
                // 6. Save changes
                if (!application.save(flush: true)) {
                    result.errors = extractValidationErrors(application)
                    return result
                }
                
                // 7. Audit changes
                auditApplicationChanges(application, originalValues, updateData)
                
                result.success = true
                result.application = application
                result.message = "Loan application updated successfully"
                
            } catch (Exception e) {
                log.error("Error updating loan application", e)
                status.setRollbackOnly()
                result.errors << "Application update failed: ${e.message}"
            }
            
            return result
        }
    }
    
    /**
     * Withdraw loan application
     */
    Map withdrawLoanApplication(Long applicationId, String reason) {
        return LoanApplication.withTransaction { status ->
            Map result = [success: false, errors: []]
            
            try {
                LoanApplication application = LoanApplication.get(applicationId)
                if (!application) {
                    result.errors << "Loan application not found"
                    return result
                }
                
                // 1. Check if application can be withdrawn
                if (!canWithdrawApplication(application)) {
                    result.errors << "Application cannot be withdrawn in current status"
                    return result
                }
                
                // 2. Update application status
                application.status = ConfigItemStatus.get(6) // Withdrawn
                application.withdrawalReason = reason
                application.withdrawalDate = new Date()
                
                // 3. Process workflow transition
                workflowManagementService.processStateTransition(
                    'LOAN_APPLICATION',
                    application.id,
                    application.status.description,
                    'WITHDRAWN',
                    [withdrawalReason: reason]
                )
                
                // 4. Save changes
                if (!application.save(flush: true)) {
                    result.errors = extractValidationErrors(application)
                    return result
                }
                
                // 5. Audit withdrawal
                auditLoanApplication('APPLICATION_WITHDRAWN', application, 'SUCCESS', reason)
                
                result.success = true
                result.message = "Loan application withdrawn successfully"
                
            } catch (Exception e) {
                log.error("Error withdrawing loan application", e)
                status.setRollbackOnly()
                result.errors << "Application withdrawal failed: ${e.message}"
            }
            
            return result
        }
    }
    
    /**
     * Get loan application details
     */
    Map getLoanApplicationDetails(Long applicationId) {
        try {
            LoanApplication application = LoanApplication.get(applicationId)
            if (!application) {
                return [success: false, error: "Loan application not found"]
            }
            
            return [
                success: true,
                application: [
                    id: application.id,
                    applicationNumber: application.applicationNumber,
                    customer: [
                        id: application.customer.id,
                        name: application.customer.displayName,
                        customerId: application.customer.customerId
                    ],
                    loanType: application.loanType?.description,
                    requestedAmount: application.requestedAmount,
                    approvedAmount: application.approvedAmount,
                    interestRate: application.interestRate,
                    tenure: application.tenure,
                    monthlyPayment: application.monthlyPayment,
                    purpose: application.loanPurpose,
                    status: application.status?.description,
                    creditScore: application.creditScore,
                    creditGrade: application.creditGrade,
                    riskRating: application.riskRating,
                    applicationDate: application.applicationDate,
                    lastUpdated: application.lastUpdated
                ],
                documents: getApplicationDocuments(application),
                workflow: getApplicationWorkflowStatus(application),
                nextSteps: getNextSteps(application)
            ]
            
        } catch (Exception e) {
            log.error("Error getting loan application details", e)
            return [success: false, error: "Failed to retrieve application details"]
        }
    }
    
    // =====================================================
    // VALIDATION METHODS
    // =====================================================
    
    /**
     * Validate loan application data
     */
    private Map validateLoanApplication(Map applicationData) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        // Customer validation
        if (!applicationData.customerId) {
            result.isValid = false
            result.errors << "Customer is required"
        }
        
        // Loan amount validation
        if (!applicationData.requestedAmount || applicationData.requestedAmount <= 0) {
            result.isValid = false
            result.errors << "Loan amount must be greater than zero"
        } else {
            BigDecimal amount = new BigDecimal(applicationData.requestedAmount.toString())
            if (amount < 1000) {
                result.isValid = false
                result.errors << "Minimum loan amount is \$1,000"
            } else if (amount > 1000000) {
                result.isValid = false
                result.errors << "Maximum loan amount is \$1,000,000"
            }
        }
        
        // Loan type validation
        if (!applicationData.loanTypeId) {
            result.isValid = false
            result.errors << "Loan type is required"
        }
        
        // Purpose validation
        if (!applicationData.loanPurpose || applicationData.loanPurpose.toString().trim().isEmpty()) {
            result.isValid = false
            result.errors << "Loan purpose is required"
        }
        
        // Income validation
        if (!applicationData.monthlyIncome || applicationData.monthlyIncome <= 0) {
            result.isValid = false
            result.errors << "Monthly income is required"
        } else {
            BigDecimal income = new BigDecimal(applicationData.monthlyIncome.toString())
            BigDecimal requestedAmount = new BigDecimal(applicationData.requestedAmount.toString())
            
            // Debt-to-income ratio check
            BigDecimal estimatedMonthlyPayment = requestedAmount * 0.1 // Rough estimate
            BigDecimal debtToIncomeRatio = estimatedMonthlyPayment / income
            
            if (debtToIncomeRatio > 0.4) {
                result.warnings << "High debt-to-income ratio detected"
            }
        }
        
        // Employment validation
        if (!applicationData.employmentType) {
            result.isValid = false
            result.errors << "Employment type is required"
        }
        
        if (!applicationData.yearsOfEmployment || applicationData.yearsOfEmployment < 0) {
            result.isValid = false
            result.errors << "Years of employment is required"
        }
        
        return result
    }
    
    /**
     * Check customer eligibility for loan
     */
    private Map checkCustomerEligibility(Long customerId) {
        Map result = [eligible: true, reasons: []]
        
        try {
            Customer customer = Customer.get(customerId)
            if (!customer) {
                result.eligible = false
                result.reasons << "Customer not found"
                return result
            }
            
            // Check customer status
            if (!customer.status || customer.status.id != 1) {
                result.eligible = false
                result.reasons << "Customer account is not active"
            }
            
            // Check existing loans
            int activeLoanCount = Loan.countByCustomerAndStatus(customer, ConfigItemStatus.get(1))
            if (activeLoanCount >= 3) {
                result.eligible = false
                result.reasons << "Customer has maximum number of active loans"
            }
            
            // Check pending applications
            int pendingApplications = LoanApplication.countByCustomerAndStatus(customer, ConfigItemStatus.get(1))
            if (pendingApplications >= 2) {
                result.eligible = false
                result.reasons << "Customer has maximum number of pending applications"
            }
            
            // Check customer age
            if (customer.birthDate) {
                int age = calculateAge(customer.birthDate)
                if (age < 18) {
                    result.eligible = false
                    result.reasons << "Customer must be at least 18 years old"
                } else if (age > 65) {
                    result.eligible = false
                    result.reasons << "Customer age exceeds maximum limit"
                }
            }
            
        } catch (Exception e) {
            log.error("Error checking customer eligibility", e)
            result.eligible = false
            result.reasons << "Eligibility check failed"
        }
        
        return result
    }
    
    // =====================================================
    // BUSINESS LOGIC METHODS
    // =====================================================
    
    /**
     * Create loan application entity
     */
    private LoanApplication createLoanApplication(Map applicationData) {
        LoanApplication application = new LoanApplication()
        
        // Basic application details
        application.customer = Customer.get(applicationData.customerId)
        application.loanType = LoanType.get(applicationData.loanTypeId)
        application.requestedAmount = new BigDecimal(applicationData.requestedAmount.toString())
        application.loanPurpose = applicationData.loanPurpose.toString()
        application.requestedTenure = applicationData.requestedTenure as Integer ?: 12
        
        // Applicant information
        application.monthlyIncome = new BigDecimal(applicationData.monthlyIncome.toString())
        application.employmentType = applicationData.employmentType.toString()
        application.employerName = applicationData.employerName?.toString()
        application.yearsOfEmployment = applicationData.yearsOfEmployment as Integer
        
        // Additional financial information
        if (applicationData.otherIncome) {
            application.otherIncome = new BigDecimal(applicationData.otherIncome.toString())
        }
        if (applicationData.monthlyExpenses) {
            application.monthlyExpenses = new BigDecimal(applicationData.monthlyExpenses.toString())
        }
        
        // Application metadata
        application.applicationDate = new Date()
        application.lastUpdated = new Date()
        
        return application
    }
    
    /**
     * Perform initial credit assessment
     */
    private Map performInitialCreditAssessment(LoanApplication application) {
        Map result = [creditScore: 0, creditGrade: 'F', riskRating: 'HIGH']
        
        try {
            int creditScore = 300 // Base score
            
            // Income factor (40% weight)
            BigDecimal monthlyIncome = application.monthlyIncome
            if (monthlyIncome > 10000) creditScore += 200
            else if (monthlyIncome > 5000) creditScore += 150
            else if (monthlyIncome > 3000) creditScore += 100
            else if (monthlyIncome > 2000) creditScore += 50
            
            // Employment stability (20% weight)
            int yearsOfEmployment = application.yearsOfEmployment
            if (yearsOfEmployment > 5) creditScore += 100
            else if (yearsOfEmployment > 2) creditScore += 75
            else if (yearsOfEmployment > 1) creditScore += 50
            else creditScore += 25
            
            // Customer history (20% weight)
            Map customerHistory = analyzeCustomerHistory(application.customer)
            creditScore += customerHistory.historyScore
            
            // Loan amount vs income ratio (20% weight)
            BigDecimal loanToIncomeRatio = application.requestedAmount / (monthlyIncome * 12)
            if (loanToIncomeRatio < 2) creditScore += 100
            else if (loanToIncomeRatio < 3) creditScore += 75
            else if (loanToIncomeRatio < 4) creditScore += 50
            else creditScore += 25
            
            // Ensure score is within valid range
            creditScore = Math.min(850, Math.max(300, creditScore))
            
            // Determine credit grade and risk rating
            String creditGrade = 'F'
            String riskRating = 'HIGH'
            
            if (creditScore >= 750) {
                creditGrade = 'A'
                riskRating = 'LOW'
            } else if (creditScore >= 700) {
                creditGrade = 'B'
                riskRating = 'LOW'
            } else if (creditScore >= 650) {
                creditGrade = 'C'
                riskRating = 'MEDIUM'
            } else if (creditScore >= 600) {
                creditGrade = 'D'
                riskRating = 'MEDIUM'
            } else if (creditScore >= 550) {
                creditGrade = 'E'
                riskRating = 'HIGH'
            }
            
            result.creditScore = creditScore
            result.creditGrade = creditGrade
            result.riskRating = riskRating
            
        } catch (Exception e) {
            log.error("Error in credit assessment", e)
        }
        
        return result
    }
    
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    
    /**
     * Generate unique application number
     */
    private String generateApplicationNumber() {
        return "LA${System.currentTimeMillis()}${(Math.random() * 1000).toInteger()}"
    }
    
    /**
     * Calculate age from birth date
     */
    private int calculateAge(Date birthDate) {
        Calendar birth = Calendar.getInstance()
        birth.setTime(birthDate)
        
        Calendar now = Calendar.getInstance()
        
        int age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR)
        
        if (now.get(Calendar.DAY_OF_YEAR) < birth.get(Calendar.DAY_OF_YEAR)) {
            age--
        }
        
        return age
    }
    
    /**
     * Analyze customer banking history
     */
    private Map analyzeCustomerHistory(Customer customer) {
        int historyScore = 50 // Base score
        
        // Customer age with bank
        if (customer.dateCreated) {
            long daysSinceCreation = (new Date().time - customer.dateCreated.time) / (24 * 60 * 60 * 1000)
            if (daysSinceCreation > 365) historyScore += 30
            else if (daysSinceCreation > 180) historyScore += 20
            else if (daysSinceCreation > 90) historyScore += 10
        }
        
        return [historyScore: historyScore]
    }
    
    /**
     * Update application with credit information
     */
    private void updateApplicationWithCreditInfo(LoanApplication application, Map creditAssessment) {
        application.creditScore = creditAssessment.creditScore
        application.creditGrade = creditAssessment.creditGrade
        application.riskRating = creditAssessment.riskRating
    }
    
    /**
     * Update application with loan terms
     */
    private void updateApplicationWithTerms(LoanApplication application, Map loanTerms) {
        application.approvedAmount = loanTerms.approvedAmount
        application.interestRate = loanTerms.interestRate
        application.tenure = loanTerms.tenure
        application.monthlyPayment = loanTerms.monthlyPayment
    }
    
    /**
     * Check if application can be updated
     */
    private boolean canUpdateApplication(LoanApplication application) {
        return application.status?.id in [1, 5] // Submitted or Pending Documents
    }
    
    /**
     * Check if application can be withdrawn
     */
    private boolean canWithdrawApplication(LoanApplication application) {
        return application.status?.id in [1, 2, 5] // Submitted, Under Review, or Pending Documents
    }
    
    /**
     * Get next steps for application
     */
    private List<String> getNextSteps(LoanApplication application) {
        List<String> steps = []
        
        switch (application.status?.id) {
            case 1: // Submitted
                steps << "Application under initial review"
                steps << "Credit assessment in progress"
                break
            case 2: // Under Review
                steps << "Detailed evaluation in progress"
                steps << "Awaiting approval decision"
                break
            case 5: // Pending Documents
                steps << "Submit required documents"
                steps << "Contact loan officer for document list"
                break
        }
        
        return steps
    }
    
    /**
     * Audit loan application events
     */
    private void auditLoanApplication(String eventType, LoanApplication application, String result, String errorMessage = null) {
        try {
            securityAuditService.logSecurityEvent([
                eventType: eventType,
                eventDescription: "Loan application event",
                applicationId: application?.id,
                applicationNumber: application?.applicationNumber,
                customerId: application?.customer?.id,
                requestedAmount: application?.requestedAmount,
                result: result,
                errorMessage: errorMessage
            ])
        } catch (Exception e) {
            log.error("Error auditing loan application", e)
        }
    }
    
    /**
     * Extract validation errors from domain object
     */
    private List<String> extractValidationErrors(def domainInstance) {
        List<String> errors = []
        domainInstance.errors.allErrors.each { error ->
            errors << error.defaultMessage
        }
        return errors
    }
    
    // Additional helper methods would be implemented here...
    private Map captureApplicationSnapshot(LoanApplication application) { return [:] }
    private Map validateApplicationUpdate(LoanApplication application, Map updateData) { return [isValid: true, errors: []] }
    private void updateApplicationData(LoanApplication application, Map updateData) { }
    private boolean hasFinancialDataChanged(Map originalValues, Map updateData) { return false }
    private void auditApplicationChanges(LoanApplication application, Map originalValues, Map updateData) { }
    private void generateDocumentChecklist(LoanApplication application) { }
    private List getApplicationDocuments(LoanApplication application) { return [] }
    private Map getApplicationWorkflowStatus(LoanApplication application) { return [:] }
}

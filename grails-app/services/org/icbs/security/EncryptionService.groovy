package org.icbs.security

import grails.gorm.transactions.Transactional
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.beans.factory.annotation.Value
import groovy.util.logging.Slf4j
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec
import javax.crypto.spec.IvParameterSpec
import java.security.SecureRandom
import java.util.Base64
import org.bouncycastle.crypto.generators.SCrypt
import java.nio.charset.StandardCharsets

/**
 * Encryption Service
 * Provides comprehensive encryption and hashing services for banking data protection
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
// @Service annotation removed for Grails 6.2.3 compatibility
@Transactional
@Slf4j
class EncryptionService {
    
    @Value('\${encryption.master.key:QwikBankaEncryptionMasterKey2025!@#$%^&*()}')
    String masterKey

    @Value('\${encryption.algorithm:AES/CBC/PKCS5Padding}')
    String encryptionAlgorithm

    @Value('\${encryption.key.algorithm:AES}')
    String keyAlgorithm

    @Value('\${bcrypt.strength:12}')
    int bcryptStrength
    
    // Password encoder for user passwords
    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder(12)
    
    // Secure random for generating IVs and salts
    private final SecureRandom secureRandom = new SecureRandom()
    
    /**
     * Hash password using BCrypt
     */
    String hashPassword(String plainPassword) {
        if (!plainPassword) {
            throw new IllegalArgumentException("Password cannot be null or empty")
        }
        
        try {
            String hashedPassword = passwordEncoder.encode(plainPassword)
            log.debug("Password hashed successfully")
            return hashedPassword
        } catch (Exception e) {
            log.error("Error hashing password", e)
            throw new SecurityException("Failed to hash password", e)
        }
    }
    
    /**
     * Verify password against BCrypt hash
     */
    boolean verifyPassword(String plainPassword, String hashedPassword) {
        if (!plainPassword || !hashedPassword) {
            return false
        }
        
        try {
            boolean matches = passwordEncoder.matches(plainPassword, hashedPassword)
            log.debug("Password verification: ${matches ? 'SUCCESS' : 'FAILURE'}")
            return matches
        } catch (Exception e) {
            log.error("Error verifying password", e)
            return false
        }
    }
    
    /**
     * Generate secure random salt
     */
    String generateSalt(int length = 32) {
        byte[] salt = new byte[length]
        secureRandom.nextBytes(salt)
        return Base64.encoder.encodeToString(salt)
    }
    
    /**
     * Encrypt sensitive data using AES encryption
     */
    String encryptSensitiveData(String plainText, String fieldType = 'GENERAL') {
        if (!plainText) {
            return null
        }
        
        try {
            // Generate field-specific key
            SecretKey secretKey = generateFieldKey(fieldType)
            
            // Generate random IV
            byte[] iv = new byte[16]
            secureRandom.nextBytes(iv)
            IvParameterSpec ivSpec = new IvParameterSpec(iv)
            
            // Initialize cipher
            Cipher cipher = Cipher.getInstance(encryptionAlgorithm)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec)
            
            // Encrypt data
            byte[] encryptedData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8))
            
            // Combine IV and encrypted data
            byte[] combined = new byte[iv.length + encryptedData.length]
            System.arraycopy(iv, 0, combined, 0, iv.length)
            System.arraycopy(encryptedData, 0, combined, iv.length, encryptedData.length)
            
            String encrypted = Base64.encoder.encodeToString(combined)
            log.debug("Data encrypted successfully for field type: ${fieldType}")
            return encrypted
            
        } catch (Exception e) {
            log.error("Error encrypting sensitive data for field type: ${fieldType}", e)
            throw new SecurityException("Failed to encrypt sensitive data", e)
        }
    }
    
    /**
     * Decrypt sensitive data using AES encryption
     */
    String decryptSensitiveData(String encryptedText, String fieldType = 'GENERAL') {
        if (!encryptedText) {
            return null
        }
        
        try {
            // Generate field-specific key
            SecretKey secretKey = generateFieldKey(fieldType)
            
            // Decode base64
            byte[] combined = Base64.decoder.decode(encryptedText)
            
            // Extract IV and encrypted data
            byte[] iv = new byte[16]
            byte[] encryptedData = new byte[combined.length - 16]
            System.arraycopy(combined, 0, iv, 0, 16)
            System.arraycopy(combined, 16, encryptedData, 0, encryptedData.length)
            
            IvParameterSpec ivSpec = new IvParameterSpec(iv)
            
            // Initialize cipher
            Cipher cipher = Cipher.getInstance(encryptionAlgorithm)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)
            
            // Decrypt data
            byte[] decryptedData = cipher.doFinal(encryptedData)
            
            String decrypted = new String(decryptedData, StandardCharsets.UTF_8)
            log.debug("Data decrypted successfully for field type: ${fieldType}")
            return decrypted
            
        } catch (Exception e) {
            log.error("Error decrypting sensitive data for field type: ${fieldType}", e)
            throw new SecurityException("Failed to decrypt sensitive data", e)
        }
    }
    
    /**
     * Generate field-specific encryption key
     */
    private SecretKey generateFieldKey(String fieldType) {
        try {
            // Combine master key with field type for field-specific keys
            String keyMaterial = masterKey + fieldType
            
            // Use SCrypt to derive key
            byte[] salt = fieldType.getBytes(StandardCharsets.UTF_8)
            byte[] derivedKey = SCrypt.generate(
                keyMaterial.getBytes(StandardCharsets.UTF_8),
                salt,
                16384, // N - CPU/memory cost parameter
                8,     // r - block size parameter
                1,     // p - parallelization parameter
                32     // key length in bytes (256 bits)
            )
            
            return new SecretKeySpec(derivedKey, keyAlgorithm)
            
        } catch (Exception e) {
            log.error("Error generating field key for type: ${fieldType}", e)
            throw new SecurityException("Failed to generate encryption key", e)
        }
    }
    
    /**
     * Encrypt PII (Personally Identifiable Information)
     */
    String encryptPII(String piiData) {
        return encryptSensitiveData(piiData, 'PII')
    }
    
    /**
     * Decrypt PII (Personally Identifiable Information)
     */
    String decryptPII(String encryptedPII) {
        return decryptSensitiveData(encryptedPII, 'PII')
    }
    
    /**
     * Encrypt financial data
     */
    String encryptFinancialData(String financialData) {
        return encryptSensitiveData(financialData, 'FINANCIAL')
    }
    
    /**
     * Decrypt financial data
     */
    String decryptFinancialData(String encryptedFinancialData) {
        return decryptSensitiveData(encryptedFinancialData, 'FINANCIAL')
    }
    
    /**
     * Encrypt account numbers
     */
    String encryptAccountNumber(String accountNumber) {
        return encryptSensitiveData(accountNumber, 'ACCOUNT_NUMBER')
    }
    
    /**
     * Decrypt account numbers
     */
    String decryptAccountNumber(String encryptedAccountNumber) {
        return decryptSensitiveData(encryptedAccountNumber, 'ACCOUNT_NUMBER')
    }
    
    /**
     * Encrypt SSN/Tax ID
     */
    String encryptSSN(String ssn) {
        return encryptSensitiveData(ssn, 'SSN')
    }
    
    /**
     * Decrypt SSN/Tax ID
     */
    String decryptSSN(String encryptedSSN) {
        return decryptSensitiveData(encryptedSSN, 'SSN')
    }
    
    /**
     * Generate secure random token
     */
    String generateSecureToken(int length = 32) {
        byte[] token = new byte[length]
        secureRandom.nextBytes(token)
        return Base64.urlEncoder.withoutPadding().encodeToString(token)
    }
    
    /**
     * Generate MFA secret
     */
    String generateMFASecret() {
        return generateSecureToken(20) // 160 bits for TOTP
    }
    
    /**
     * Hash data using SHA-256
     */
    String hashSHA256(String data) {
        if (!data) {
            return null
        }
        
        try {
            java.security.MessageDigest digest = java.security.MessageDigest.getInstance("SHA-256")
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8))
            return Base64.encoder.encodeToString(hash)
        } catch (Exception e) {
            log.error("Error hashing data with SHA-256", e)
            throw new SecurityException("Failed to hash data", e)
        }
    }
    
    /**
     * Generate HMAC signature
     */
    String generateHMAC(String data, String key) {
        if (!data || !key) {
            throw new IllegalArgumentException("Data and key cannot be null")
        }
        
        try {
            javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256")
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256")
            mac.init(secretKey)
            byte[] signature = mac.doFinal(data.getBytes(StandardCharsets.UTF_8))
            return Base64.encoder.encodeToString(signature)
        } catch (Exception e) {
            log.error("Error generating HMAC signature", e)
            throw new SecurityException("Failed to generate HMAC signature", e)
        }
    }
    
    /**
     * Verify HMAC signature
     */
    boolean verifyHMAC(String data, String signature, String key) {
        try {
            String expectedSignature = generateHMAC(data, key)
            return constantTimeEquals(signature, expectedSignature)
        } catch (Exception e) {
            log.error("Error verifying HMAC signature", e)
            return false
        }
    }
    
    /**
     * Constant time string comparison to prevent timing attacks
     */
    private boolean constantTimeEquals(String a, String b) {
        if (a == null || b == null) {
            return a == b
        }
        
        if (a.length() != b.length()) {
            return false
        }
        
        int result = 0
        for (int i = 0; i < a.length(); i++) {
            result |= a.charAt(i) ^ b.charAt(i)
        }
        
        return result == 0
    }
    
    /**
     * Mask sensitive data for display
     */
    String maskSensitiveData(String data, String maskType = 'DEFAULT') {
        if (!data) {
            return data
        }
        
        switch (maskType) {
            case 'ACCOUNT_NUMBER':
                return maskAccountNumber(data)
            case 'SSN':
                return maskSSN(data)
            case 'CREDIT_CARD':
                return maskCreditCard(data)
            case 'EMAIL':
                return maskEmail(data)
            case 'PHONE':
                return maskPhone(data)
            default:
                return maskDefault(data)
        }
    }
    
    /**
     * Mask account number (show last 4 digits)
     */
    private String maskAccountNumber(String accountNumber) {
        if (accountNumber.length() <= 4) {
            return "*" * accountNumber.length()
        }
        return "*" * (accountNumber.length() - 4) + accountNumber.substring(accountNumber.length() - 4)
    }
    
    /**
     * Mask SSN (show last 4 digits)
     */
    private String maskSSN(String ssn) {
        if (ssn.length() <= 4) {
            return "*" * ssn.length()
        }
        return "***-**-" + ssn.substring(ssn.length() - 4)
    }
    
    /**
     * Mask credit card (show last 4 digits)
     */
    private String maskCreditCard(String creditCard) {
        if (creditCard.length() <= 4) {
            return "*" * creditCard.length()
        }
        return "**** **** **** " + creditCard.substring(creditCard.length() - 4)
    }
    
    /**
     * Mask email (show first character and domain)
     */
    private String maskEmail(String email) {
        int atIndex = email.indexOf('@')
        if (atIndex <= 0) {
            return "*" * email.length()
        }
        
        String localPart = email.substring(0, atIndex)
        String domain = email.substring(atIndex)
        
        if (localPart.length() <= 1) {
            return "*" + domain
        }
        
        return localPart.charAt(0) + "*" * (localPart.length() - 1) + domain
    }
    
    /**
     * Mask phone number (show last 4 digits)
     */
    private String maskPhone(String phone) {
        if (phone.length() <= 4) {
            return "*" * phone.length()
        }
        return "*" * (phone.length() - 4) + phone.substring(phone.length() - 4)
    }
    
    /**
     * Default masking (show first and last character)
     */
    private String maskDefault(String data) {
        if (data.length() <= 2) {
            return "*" * data.length()
        }
        return data.charAt(0) + "*" * (data.length() - 2) + data.charAt(data.length() - 1)
    }
    
    /**
     * Validate password strength
     */
    Map<String, Object> validatePasswordStrength(String password) {
        Map<String, Object> result = [:]
        result.score = 0
        result.feedback = []
        result.isValid = false
        
        if (!password) {
            result.feedback << "Password is required"
            return result
        }
        
        // Length check
        if (password.length() >= 8) {
            result.score += 20
        } else {
            result.feedback << "Password must be at least 8 characters long"
        }
        
        // Uppercase check
        if (password.matches(/.*[A-Z].*/)) {
            result.score += 20
        } else {
            result.feedback << "Password must contain at least one uppercase letter"
        }
        
        // Lowercase check
        if (password.matches(/.*[a-z].*/)) {
            result.score += 20
        } else {
            result.feedback << "Password must contain at least one lowercase letter"
        }
        
        // Digit check
        if (password.matches(/.*\d.*/)) {
            result.score += 20
        } else {
            result.feedback << "Password must contain at least one digit"
        }
        
        // Special character check
        if (password.matches(/.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?].*/)) {
            result.score += 20
        } else {
            result.feedback << "Password must contain at least one special character"
        }
        
        // Bonus points for length
        if (password.length() >= 12) {
            result.score += 10
        }
        
        // Penalty for common patterns
        if (password.toLowerCase().contains("password") || 
            password.toLowerCase().contains("123456") ||
            password.toLowerCase().contains("qwerty")) {
            result.score -= 30
            result.feedback << "Password contains common patterns"
        }
        
        result.isValid = result.score >= 80 && result.feedback.isEmpty()
        result.strength = getPasswordStrengthLevel(result.score)
        
        return result
    }
    
    /**
     * Get password strength level
     */
    private String getPasswordStrengthLevel(int score) {
        if (score >= 90) return 'VERY_STRONG'
        if (score >= 80) return 'STRONG'
        if (score >= 60) return 'MEDIUM'
        if (score >= 40) return 'WEAK'
        return 'VERY_WEAK'
    }
}

package org.icbs.security

import org.icbs.admin.UserMaster
import org.springframework.stereotype.Service
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

@Slf4j
@Service
@Transactional(readOnly = true) // Permissions are typically read-only operations
class PermissionService {

    /**
     * Checks if a user has a specific permission.
     * @param user The UserMaster instance.
     * @param permissionCode The code of the permission to check (e.g., "CUSTOMER_READ", "CUSTOMER_EDIT").
     * @return True if the user has the permission, false otherwise.
     */
    boolean hasPermission(UserMaster user, String permissionCode) {
        if (!user || !permissionCode) {
            log.warn("hasPermission called with null user or permissionCode. Returning false.")
            return false
        }

        // Superuser bypass for all permissions
        if (user.username == "admin") { // Assuming 'admin' is a superuser
            return true
        }

        // Eagerly fetch roles and their permissions to avoid N+1 selects
        UserMaster userWithRolesAndPermissions = UserMaster.where { id == user.id }
            .join('roles', 'left')
            .join('roles.permissions', 'left')
            .find()

        if (!userWithRolesAndPermissions) {
            log.warn("User with ID {} not found or no roles/permissions fetched for permission check.", user.id)
            return false
        }

        boolean granted = userWithRolesAndPermissions.roles.any { role ->
            role.permissions.any { permission ->
                permission.code == permissionCode && permission.configItemStatus?.code == 'ACTIVE'
            }
        }

        if (!granted) {
            log.debug("Permission denied for user {} (ID: {}) for permission {}.", user.username, user.id, permissionCode)
        }

        return granted
    }

    /**
     * Checks if a user has any of the given permissions.
     * @param user The UserMaster instance.
     * @param permissionCodes A list of permission codes to check.
     * @return True if the user has at least one of the permissions, false otherwise.
     */
    boolean hasAnyPermission(UserMaster user, List<String> permissionCodes) {
        if (!user || !permissionCodes || permissionCodes.isEmpty()) {
            log.warn("hasAnyPermission called with null user or empty permissionCodes. Returning false.")
            return false
        }

        // Superuser bypass
        if (user.username == "admin") {
            return true
        }

        UserMaster userWithRolesAndPermissions = UserMaster.where { id == user.id }
            .join('roles', 'left')
            .join('roles.permissions', 'left')
            .find()

        if (!userWithRolesAndPermissions) {
            log.warn("User with ID {} not found or no roles/permissions fetched for any permission check.", user.id)
            return false
        }

        boolean granted = userWithRolesAndPermissions.roles.any { role ->
            role.permissions.any { permission ->
                permission.code in permissionCodes && permission.configItemStatus?.code == 'ACTIVE'
            }
        }

        if (!granted) {
            log.debug("Permission denied for user {} (ID: {}) for any of permissions {}.", user.username, user.id, permissionCodes)
        }
        return granted
    }

    /**
     * Checks if a user has all of the given permissions.
     * @param user The UserMaster instance.
     * @param permissionCodes A list of permission codes to check.
     * @return True if the user has all of the permissions, false otherwise.
     */
    boolean hasAllPermissions(UserMaster user, List<String> permissionCodes) {
        if (!user || !permissionCodes || permissionCodes.isEmpty()) {
            log.warn("hasAllPermissions called with null user or empty permissionCodes. Returning false.")
            return false
        }

        // Superuser bypass
        if (user.username == "admin") {
            return true
        }

        UserMaster userWithRolesAndPermissions = UserMaster.where { id == user.id }
            .join('roles', 'left')
            .join('roles.permissions', 'left')
            .find()

        if (!userWithRolesAndPermissions) {
            log.warn("User with ID {} not found or no roles/permissions fetched for all permissions check.", user.id)
            return false
        }

        // Get all unique permission codes the user has through their roles
        Set<String> userPermissionCodes = userWithRolesAndPermissions.roles.collectMany { it.permissions }.collect { it.code }.toSet()

        boolean granted = permissionCodes.every { requiredPermission ->
            userPermissionCodes.contains(requiredPermission) &&
            // Additionally, verify that the actual Permission object associated with this code is active
            // This implies that userPermissionCodes should ideally store the actual Permission objects or their status
            // For simplicity, assuming userPermissionCodes is a set of codes, and we trust that the join fetched active ones.
            // A more robust check might involve re-fetching the Permission by code and checking its status.
            Permission.findByCode(requiredPermission)?.configItemStatus?.code == 'ACTIVE'
        }

        if (!granted) {
            log.debug("Permission denied for user {} (ID: {}) for all of permissions {}. Missing: {}",
                user.username, user.id, permissionCodes, permissionCodes.findAll { !userPermissionCodes.contains(it) })
        }
        return granted
    }

    /**
     * Retrieves a list of all active permissions a user has.
     * @param user The UserMaster instance.
     * @return A list of active Permission objects.
     */
    List<Permission> getUserActivePermissions(UserMaster user) {
        if (!user) {
            log.warn("getUserActivePermissions called with null user. Returning empty list.")
            return []
        }

        // Superuser bypass
        if (user.username == "admin") {
            return Permission.findAllByConfigItemStatus(ConfigItemStatus.findByCode('ACTIVE'))
        }

        UserMaster userWithRolesAndPermissions = UserMaster.where { id == user.id }
            .join('roles', 'left')
            .join('roles.permissions', 'left')
            .find()

        if (!userWithRolesAndPermissions) {
            return []
        }

        return userWithRolesAndPermissions.roles.collectMany { it.permissions }.findAll { it.configItemStatus?.code == 'ACTIVE' }.unique()
    }
} 
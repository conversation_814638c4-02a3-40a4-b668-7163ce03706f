package org.icbs.security

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import java.util.concurrent.TimeUnit

@Slf4j
@Service // Mark as a Spring service
class RateLimitingService {

    // Injected from application.yml
    @Value("\${security.rateLimiting.maxRequests:100}")
    int maxRequestsPerWindow

    @Value("\${security.rateLimiting.windowSeconds:60}")
    int windowSeconds

    private final Cache<String, Integer> requestCounts

    RateLimitingService() {
        this.requestCounts = Caffeine.newBuilder()
            .expireAfterWrite(windowSeconds, TimeUnit.SECONDS)
            .maximumSize(10000) // Max entries in cache
            .build()
    }

    /**
     * Checks if the given key (e.g., IP address or userId) has exceeded the rate limit.
     * If not, increments the request count.
     * @param key The unique key to track (e.g., IP address, username, API key).
     * @return true if the rate limit is exceeded, false otherwise.
     */
    boolean isRateLimited(String key) {
        Integer currentCount = requestCounts.get(key, k -> 0) // Get current count, or 0 if not present

        if (currentCount >= maxRequestsPerWindow) {
            log.warn("Rate limit exceeded for key: {}", key)
            return true
        } else {
            requestCounts.put(key, currentCount + 1)
            log.debug("Incremented request count for key {}. Current count: {}", key, currentCount + 1)
            return false
        }
    }

    /**
     * Resets the rate limit for a specific key. Useful after a successful operation
     * or a period of inactivity.
     * @param key The key to reset.
     */
    void resetRateLimit(String key) {
        requestCounts.invalidate(key)
        log.debug("Rate limit reset for key: {}", key)
    }

    /**
     * Clears all entries from the rate limiting cache.
     */
    void clearAllLimits() {
        requestCounts.invalidateAll()
        log.info("All rate limits cleared.")
    }
} 
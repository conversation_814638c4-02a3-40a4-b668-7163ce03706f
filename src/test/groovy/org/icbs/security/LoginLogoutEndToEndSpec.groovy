package org.icbs.security

import geb.spock.GebSpec
import grails.gorm.transactions.Rollback
import grails.test.mixin.integration.Integration
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.Gender
import org.icbs.lov.Lov
import org.icbs.admin.Designation
import org.icbs.admin.Role
import org.icbs.security.Permission

@Integration
@Rollback
class LoginLogoutEndToEndSpec extends GebSpec {

    def setupSpec() {
        // Setup for all tests in this spec
        def activeStatus = new ConfigItemStatus(code: 'ACTIVE', description: 'Active').save(flush: true)

        def loginUserRole = new Role(code: 'LOGIN_USER', name: 'Login User', configItemStatus: activeStatus).save(flush: true)
        // Grant necessary permissions for a user to be able to log in and access landing page
        new Permission(code: 'HOME_VIEW', name: 'View Home', configItemStatus: activeStatus).save(flush: true)

        def testUser = new UserMaster(
            username: "loginuser",
            password: "password", // Password will be BCrypted by SecurePasswordService on save
            name1: "Login",
            name3: "Test",
            birthdate: new Date() - 30*365,
            gender: new Gender(code: 'M', description: 'Male').save(flush: true),
            address1: "123 Login St",
            designation: new Designation(code: 'EMP', description: 'Employee').save(flush: true),
            branch: new Branch(code: 1, name: 'Main Branch').save(flush: true),
            employmentType: new Lov(code: 'FULL', description: 'Full-time').save(flush: true),
            expiryDate: new Date() + 30,
            configItemStatus: activeStatus,
            isFirstLogin: false
        ).save(flush: true)
        testUser.addToRoles(loginUserRole)
        testUser.save(flush: true)

        // User for failed attempts/rate limiting
        new UserMaster(
            username: "failuser",
            password: "wrongpassword", // Will be hashed but won't match correct password
            name1: "Fail",
            name3: "User",
            birthdate: new Date() - 25*365,
            gender: new Gender(code: 'F', description: 'Female').save(flush: true),
            address1: "456 Fail St",
            designation: new Designation(code: 'EMP', description: 'Employee').save(flush: true),
            branch: new Branch(code: 2, name: 'Branch B').save(flush: true),
            employmentType: new Lov(code: 'PART', description: 'Part-time').save(flush: true),
            expiryDate: new Date() + 30,
            configItemStatus: activeStatus,
            isFirstLogin: false
        ).save(flush: true)
    }

    void "test successful login and logout"() {
        given:
        browser.go LoginPage

        when:
        usernameField.value("loginuser")
        passwordField.value("password")
        loginButton.click()

        then:
        // Assuming successful login redirects to a dashboard or landing page
        browser.currentUrl.contains("/home/<USER>")
        // Check for presence of user-specific elements or a success message
        // Example: $("div.welcome-message", text: "Welcome, Login Test!").present
        
        when:
        // Simulate logout (this depends on how logout is triggered in the UI)
        // For example, if there's a logout button with a specific ID or class
        // $("#logoutButton").click()
        // For now, we will simulate a direct URL hit to the logout controller action
        browser.go("/logout")

        then:
        // Assuming logout redirects to the login page or a public home page
        browser.currentUrl.contains("/login")
        // Check for success message on logout if applicable
        // successMessage.text().contains("You have been successfully logged out")
    }

    void "test failed login due to invalid credentials"() {
        given:
        browser.go LoginPage

        when:
        usernameField.value("loginuser")
        passwordField.value("wrongpassword")
        loginButton.click()

        then:
        at LoginPage // Should remain on the login page
        errorMessage.text().contains("Invalid username or password. Please try again.")
    }

    void "test account locking after multiple failed attempts"() {
        given:
        browser.go LoginPage
        def failUser = UserMaster.findByUsername("failuser")

        and:
        // Simulate multiple failed login attempts to trigger account locking
        // This relies on the `loginAttemptService.checkAttempts` logic
        // Adjust the number of attempts based on `security.maxLoginAttempts` configuration
        for (int i = 0; i < 5; i++) { // Assuming max 5 failed attempts before locking
            usernameField.value(failUser.username)
            passwordField.value("incorrect")
            loginButton.click()
            at LoginPage
            errorMessage.text().contains("Invalid username or password. Please try again.")
        }

        when:
        // Attempt one more login after the account should be locked
        usernameField.value(failUser.username)
        passwordField.value("incorrect")
        loginButton.click()

        then:
        at LoginPage
        errorMessage.text().contains("User is locked because of exceeding maximum number of login attempts. Contact Administrator.")
        // Verify the user's status in the database if possible (requires injecting UserMaster.get(id) or similar)
        UserMaster.get(failUser.id).hasExceededMaxLogin == true
    }

    void "test rate limiting for too many requests from same IP"() {
        given:
        browser.go LoginPage
        // This test is challenging to implement purely via Geb without direct control over the server's IP tracking
        // or the RateLimitingService's internal state. It assumes that multiple rapid requests from the
        // same client (Geb's browser instance) will be detected.
        // The `security.rateLimiting.maxRequests` and `security.rateLimiting.windowSeconds` need to be considered.

        // We'll perform login attempts rapidly until the rate limit is hit.
        // Assuming `maxRequests` is 100 and `windowSeconds` is 60 as per application.yml
        // We will make more than `maxRequests` attempts to ensure rate limiting is triggered.
        def username = "someuser"
        def password = "somepassword"

        // Attempt to log in more times than maxRequests (e.g., maxRequests + 5)
        // This will likely result in HTTP 429 Too Many Requests response from the server
        // for subsequent attempts.
        for (int i = 0; i < 105; i++) { // 100 max requests + 5 extra to hit the limit
            browser.go LoginPage
            usernameField.value(username)
            passwordField.value(password)
            loginButton.click()
            // We might not always get to the error message on the page if 429 is returned early
            // We should check the response status directly.
            if (browser.driver.getStatusCode() == 429) {
                break // Rate limit hit
            }
        }

        then:
        browser.driver.getStatusCode() == 429
        browser.rawPage.webDriver.pageSource.contains("Too many login attempts from this IP address.")
    }

    void "test password expiry redirect"() {
        given:
        def expiredPwdUser = new UserMaster(
            username: "expireduser",
            password: "password",
            name1: "Expired",
            name3: "User",
            birthdate: new Date() - 30*365,
            gender: new Gender(code: 'M', description: 'Male').save(flush: true),
            address1: "789 Expired St",
            designation: new Designation(code: 'EMP', description: 'Employee').save(flush: true),
            branch: new Branch(code: 3, name: 'Branch C').save(flush: true),
            employmentType: new Lov(code: 'FULL', description: 'Full-time').save(flush: true),
            expiryDate: new Date() + 30,
            expiryPwdDate: new Date() - 1, // Password expired yesterday
            configItemStatus: ConfigItemStatus.findByCode('ACTIVE'),
            isFirstLogin: false
        ).save(flush: true)
        
        def loginUserRole = Role.findByCode('LOGIN_USER')
        expiredPwdUser.addToRoles(loginUserRole)
        expiredPwdUser.save(flush: true)

        browser.go LoginPage

        when:
        usernameField.value("expireduser")
        passwordField.value("password")
        loginButton.click()

        then:
        // Assuming it redirects to a change password page
        browser.currentUrl.contains("/userMaster/changePassword")
        flash.error.contains("Password will expire in")
    }
} 